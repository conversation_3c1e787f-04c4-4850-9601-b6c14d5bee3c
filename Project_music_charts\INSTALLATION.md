# 🚀 Music Charts Scraper - Installation Guide

Quick setup guide for the Music Charts Scraper with Supabase integration.

## 📋 Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Supabase account and project

## 🛠️ Installation Methods

### Method 1: Automatic Setup (Recommended)

1. **Navigate to the project directory:**
   ```bash
   cd Project_music_charts
   ```

2. **Run the setup script:**
   ```bash
   python setup.py
   ```

   This will:
   - Install all required packages
   - Check your .env file
   - Verify package imports
   - Create necessary folders

### Method 2: Manual Installation

1. **Install dependencies:**
   ```bash
   cd Project_music_charts
   pip install -r requirements.txt
   ```

2. **Create .env file** (in parent directory):
   ```bash
   # Create .env file with your Supabase credentials
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_KEY=your_supabase_anon_key
   ```

## 📦 Required Packages

The `requirements.txt` includes:

### Core Dependencies
- `requests` - HTTP requests for web scraping
- `beautifulsoup4` - HTML parsing
- `lxml` - XML/HTML parser
- `supabase` - Supabase Python client
- `python-dotenv` - Environment variable management

### Data Handling
- `pandas` - Data manipulation
- `openpyxl` - Excel file support

### Web Scraping
- `selenium` - Browser automation
- `html2text` - HTML to text conversion
- `readability-lxml` - Content extraction

### Utilities
- `pydantic` - Data validation
- `tiktoken` - Token counting

### Optional
- `streamlit` - Web app framework
- `openai` - OpenAI API integration

## 🔧 Setup Verification

After installation, verify your setup:

```bash
cd Project_music_charts
python test_supabase_connection.py
```

Expected output:
```
✅ Successfully inserted 2 test records!
✅ Successfully read 2 records!
✅ Test data cleaned up!
🎉 All tests passed!
```

## 🗂️ Project Structure

After setup, your directory should look like:

```
Project_music_charts/
├── requirements.txt           # Package dependencies
├── setup.py                  # Automatic setup script
├── INSTALLATION.md           # This file
├── SUPABASE_SETUP_GUIDE.md   # Complete setup guide
├── singles_scraper.py        # Singles charts scraper
├── albums_scraper.py         # Albums charts scraper
├── test_supabase_connection.py # Connection test
├── supabase_tables.sql       # Database schema
├── singles_charts_data/      # Local data backup
├── albums_charts_data/       # Local data backup
└── musics_charts_datas/      # Chart dates data
```

## 🚨 Troubleshooting

### Common Issues

**1. Package Installation Fails**
```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Then install requirements
pip install -r requirements.txt
```

**2. Permission Errors**
```bash
# Use user installation
pip install --user -r requirements.txt
```

**3. Virtual Environment (Recommended)**
```bash
# Create virtual environment
python -m venv venv

# Activate (Windows)
venv\Scripts\activate

# Activate (Mac/Linux)
source venv/bin/activate

# Install requirements
pip install -r requirements.txt
```

**4. Missing .env File**
Create `.env` file in the parent directory with:
```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
```

## 🎯 Next Steps

1. **Complete Supabase Setup:**
   - Follow `SUPABASE_SETUP_GUIDE.md`
   - Create database tables
   - Test connection

2. **Start Scraping:**
   ```bash
   python singles_scraper.py
   python albums_scraper.py
   ```

3. **Monitor Progress:**
   - Check Supabase dashboard
   - View local JSON backups
   - Monitor console output

## 📞 Support

If you encounter issues:
1. Check the `SUPABASE_SETUP_GUIDE.md` for detailed instructions
2. Verify all packages are installed correctly
3. Ensure your Supabase credentials are correct
4. Check that database tables are created

Happy scraping! 🎵
