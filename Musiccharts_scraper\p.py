import requests
from bs4 import BeautifulSoup
import re
import json
import time
from datetime import datetime
import sys

class MusicChartsArchiveScraper:
    def __init__(self):
        self.base_url = "https://musicchartsarchive.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.all_data = {
            'singles_charts': {},
            'album_charts': {}
        }
        self.decades = ['2020s', '2010s', '2000s', '1990s', '1980s', '1970s']
    
    def get_page(self, url, retries=3):
        """Get page content with retry mechanism"""
        for attempt in range(retries):
            try:
                print(f"Fetching: {url}")
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                return response.text
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < retries - 1:
                    time.sleep(2)
                else:
                    print(f"Failed to fetch {url} after {retries} attempts")
                    return None
    
    def extract_dates_from_page(self, html_content):
        """Extract all dates from a chart year page"""
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        dates = []
        
        # Find all text that matches date patterns
        text_content = soup.get_text()
        
        # Pattern to match dates like "Jan 4, 2025", "Feb 1, 2025", etc.
        date_pattern = r'([A-Z][a-z]{2}\s+\d{1,2},\s+\d{4})'
        found_dates = re.findall(date_pattern, text_content)
        
        # Remove duplicates while preserving order
        seen = set()
        for date in found_dates:
            if date not in seen:
                dates.append(date)
                seen.add(date)
        
        return dates
    
    def get_years_for_decade(self, decade):
        """Get all available years for a given decade"""
        decade_url = f"{self.base_url}/{decade}"
        html_content = self.get_page(decade_url)
        
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        years = []
        
        # Extract years from the decade page
        text_content = soup.get_text()
        
        # Extract 4-digit years
        year_pattern = r'\b(19\d{2}|20\d{2})\b'
        found_years = re.findall(year_pattern, text_content)
        
        # Remove duplicates and sort in REVERSE order (newest first)
        # Special handling for 2020s to get 2025, 2024, 2023, 2022, 2021, 2020
        if decade == '2020s':
            years = sorted(list(set(found_years)), reverse=True)
        else:
            years = sorted(list(set(found_years)), reverse=True)  # All decades in reverse order
        
        print(f"Found years for {decade}: {years}")
        return years
    
    def scrape_chart_type_for_decade(self, chart_type, decade):
        """Scrape all dates for a specific chart type and decade"""
        print(f"\n=== Scraping {chart_type} for {decade} ===")
        
        years = self.get_years_for_decade(decade)
        decade_data = {}
        
        for year in years:
            print(f"\nScraping {chart_type} for year {year}...")
            chart_url = f"{self.base_url}/{chart_type}/{year}"
            
            html_content = self.get_page(chart_url)
            dates = self.extract_dates_from_page(html_content)
            
            if dates:
                decade_data[year] = dates
                print(f"Found {len(dates)} dates for {year}")
                print(f"Sample dates: {dates[:3]}{'...' if len(dates) > 3 else ''}")
            else:
                print(f"No dates found for {year}")
            
            # Be respectful to the server
            time.sleep(1)
        
        return decade_data
    
    def scrape_all_data(self):
        """Main method to scrape all data"""
        print("Starting comprehensive scraping of Music Charts Archive...")
        print(f"Target decades: {self.decades}")
        print("Note: Years within each decade will be scraped in reverse chronological order (newest first)")
        
        start_time = datetime.now()
        
        # Scrape Singles Charts
        print("\n" + "="*50)
        print("SCRAPING SINGLES CHARTS")
        print("="*50)
        
        for decade in self.decades:
            try:
                decade_data = self.scrape_chart_type_for_decade('singles-charts', decade)
                if decade_data:
                    self.all_data['singles_charts'][decade] = decade_data
                    print(f"✓ Completed {decade} singles charts")
                else:
                    print(f"✗ No data found for {decade} singles charts")
            except Exception as e:
                print(f"✗ Error scraping {decade} singles charts: {e}")
                continue
        
        # Scrape Album Charts
        print("\n" + "="*50)
        print("SCRAPING ALBUM CHARTS")
        print("="*50)
        
        for decade in self.decades:
            try:
                decade_data = self.scrape_chart_type_for_decade('album-charts', decade)
                if decade_data:
                    self.all_data['album_charts'][decade] = decade_data
                    print(f"✓ Completed {decade} album charts")
                else:
                    print(f"✗ No data found for {decade} album charts")
            except Exception as e:
                print(f"✗ Error scraping {decade} album charts: {e}")
                continue
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n" + "="*50)
        print("SCRAPING COMPLETED!")
        print("="*50)
        print(f"Total time taken: {duration}")
        self.print_summary()
    
    def print_summary(self):
        """Print a summary of scraped data"""
        print("\n=== SCRAPING SUMMARY ===")
        
        total_dates = 0
        
        for chart_type in ['singles_charts', 'album_charts']:
            print(f"\n{chart_type.upper().replace('_', ' ')}:")
            chart_total = 0
            
            for decade, decade_data in self.all_data[chart_type].items():
                decade_total = sum(len(dates) for dates in decade_data.values())
                chart_total += decade_total
                print(f"  {decade}: {len(decade_data)} years, {decade_total} dates")
                
                # Show years in the order they were processed (reverse chronological)
                if decade_data:
                    all_years = list(decade_data.keys())
                    sample_years = all_years[:3]
                    print(f"    Years (newest first): {sample_years}{'...' if len(all_years) > 3 else ''}")
            
            total_dates += chart_total
            print(f"  {chart_type} total: {chart_total} dates")
        
        print(f"\nGRAND TOTAL: {total_dates} dates scraped")
    
    def save_to_json(self, filename="music_charts_dates.json"):
        """Save all scraped data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.all_data, f, indent=2, ensure_ascii=False)
            print(f"\n✓ Data saved to {filename}")
        except Exception as e:
            print(f"✗ Error saving to JSON: {e}")
    
    def save_to_text(self, filename="music_charts_dates.txt"):
        """Save all scraped data to a readable text file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("MUSIC CHARTS ARCHIVE - ALL DATES\n")
                f.write("="*50 + "\n\n")
                f.write("Note: Years are listed in reverse chronological order (newest first)\n\n")
                
                for chart_type in ['singles_charts', 'album_charts']:
                    f.write(f"{chart_type.upper().replace('_', ' ')}\n")
                    f.write("-" * 30 + "\n")
                    
                    for decade, decade_data in self.all_data[chart_type].items():
                        f.write(f"\n{decade}:\n")
                        
                        # Years are already in reverse order from scraping
                        for year, dates in decade_data.items():
                            f.write(f"  {year} ({len(dates)} dates):\n")
                            for date in dates:
                                f.write(f"    {date}\n")
                    
                    f.write("\n" + "="*50 + "\n")
            
            print(f"✓ Data saved to {filename}")
        except Exception as e:
            print(f"✗ Error saving to text file: {e}")
    
    def get_all_dates_as_list(self):
        """Get all dates as a flat list"""
        all_dates = []
        
        for chart_type in ['singles_charts', 'album_charts']:
            for decade, decade_data in self.all_data[chart_type].items():
                # Years are already in reverse order from scraping
                for year, dates in decade_data.items():
                    all_dates.extend(dates)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_dates = []
        for date in all_dates:
            if date not in seen:
                unique_dates.append(date)
                seen.add(date)
        
        return unique_dates

def main():
    print("Music Charts Archive Scraper")
    print("=" * 40)
    print("Configured to scrape years in reverse chronological order (newest first)")
    print("For 2020s: 2025 → 2024 → 2023 → 2022 → 2021 → 2020")
    print()
    
    scraper = MusicChartsArchiveScraper()
    
    try:
        # Start scraping
        scraper.scrape_all_data()
        
        # Save results
        scraper.save_to_json()
        scraper.save_to_text()
        
        # Get all dates as a simple list
        all_dates_list = scraper.get_all_dates_as_list()
        print(f"\nTotal unique dates found: {len(all_dates_list)}")
        print("First 10 dates:", all_dates_list[:10])
        print("Last 10 dates:", all_dates_list[-10:])
        
    except KeyboardInterrupt:
        print("\n\nScraping interrupted by user!")
        print("Current data:")
        scraper.print_summary()
        
        # Save partial results
        scraper.save_to_json("partial_music_charts_dates.json")
        scraper.save_to_text("partial_music_charts_dates.txt")
        
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()