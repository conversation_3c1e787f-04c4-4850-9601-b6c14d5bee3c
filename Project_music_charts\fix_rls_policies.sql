-- Fix RLS Policies for Music Charts Tables
-- Run this in your Supabase SQL Editor to fix the security policy issue

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Allow authenticated users to read singles_charts" ON singles_charts;
DROP POLICY IF EXISTS "Allow authenticated users to insert singles_charts" ON singles_charts;
DROP POLICY IF EXISTS "Allow authenticated users to read albums_charts" ON albums_charts;
DROP POLICY IF EXISTS "Allow authenticated users to insert albums_charts" ON albums_charts;

-- Create more permissive policies that allow API key access
CREATE POLICY "Allow all operations on singles_charts" ON singles_charts
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on albums_charts" ON albums_charts
    FOR ALL USING (true) WITH CHECK (true);

-- Alternative: Disable RLS completely (simpler approach)
-- Uncomment these lines if you want to disable <PERSON><PERSON> entirely:
-- ALTER TABLE singles_charts DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE albums_charts DISABLE ROW LEVEL SECURITY;
