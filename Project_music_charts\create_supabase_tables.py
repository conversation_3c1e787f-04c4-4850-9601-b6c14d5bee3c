#!/usr/bin/env python3
"""
Create Supabase Tables for Music Charts
এই script দিয়ে Supabase database এ table create করুন
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables.")
    sys.exit(1)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def check_tables():
    """Check if tables exist"""
    
    print("🚀 Checking Supabase tables...")
    
    # Test if singles_charts table exists
    try:
        result = supabase.table("singles_charts").select("*").limit(1).execute()
        print("✅ singles_charts table exists!")
        singles_exists = True
    except Exception as e:
        print(f"❌ singles_charts table doesn't exist: {e}")
        singles_exists = False
    
    # Test if albums_charts table exists  
    try:
        result = supabase.table("albums_charts").select("*").limit(1).execute()
        print("✅ albums_charts table exists!")
        albums_exists = True
    except Exception as e:
        print(f"❌ albums_charts table doesn't exist: {e}")
        albums_exists = False
    
    return singles_exists, albums_exists

def test_insert_and_delete():
    """Test inserting and deleting data"""
    
    print("\n🧪 Testing data operations...")
    
    # Test data for singles
    test_single = {
        "single_title": "Test Song",
        "artist": "Test Artist", 
        "chart_date": "2025-01-01",
        "position": 1,
        "url": "https://example.com/test"
    }
    
    try:
        # Insert test record
        print("📤 Inserting test single...")
        result = supabase.table("singles_charts").insert(test_single).execute()
        
        if result.data:
            print("✅ Test single inserted successfully!")
            record_id = result.data[0]['id']
            
            # Delete test record
            print("🗑️ Cleaning up test single...")
            delete_result = supabase.table("singles_charts").delete().eq('id', record_id).execute()
            print("✅ Test single deleted successfully!")
            return True
        else:
            print("❌ Failed to insert test single")
            return False
            
    except Exception as e:
        print(f"❌ Error testing singles table: {e}")
        return False

def main():
    print("🎵 Music Charts - Supabase Setup")
    print("=" * 50)
    
    # Check if tables exist
    singles_exists, albums_exists = check_tables()
    
    if not singles_exists or not albums_exists:
        print("\n❌ Tables don't exist. Please create them manually:")
        print("\n1. Go to your Supabase Dashboard: https://supabase.com/dashboard")
        print("2. Go to Table Editor")
        print("3. Create tables using the SQL script or manual method")
        print("4. Check the MANUAL_TABLE_CREATION.md file for detailed instructions")
        return
    
    # Test operations
    test_success = test_insert_and_delete()
    
    if test_success:
        print("\n🎉 All tests passed! Your Supabase setup is working correctly.")
        print("✅ You can now run your music charts scrapers!")
        print("\nRun your scrapers:")
        print("python singles_scraper.py")
        print("python albums_scraper.py")
    else:
        print("\n⚠️ Tests failed. Please check your table permissions.")

if __name__ == "__main__":
    main()
