# 🎵 Music Charts Scraper - Supabase Integration Guide

This guide will help you set up your music charts scraper to store data directly in Supabase database.

## 📋 Prerequisites

✅ You already have:
- Supabase account and project
- SUPABASE_URL and SUPABASE_KEY in your `.env` file
- Python packages installed (supabase, python-dotenv, etc.)

## 🚀 Setup Steps

### Step 1: Create Database Tables

1. **Open your Supabase Dashboard**
   - Go to https://supabase.com/dashboard
   - Select your project

2. **Run the SQL Script**
   - Go to "SQL Editor" in the left sidebar
   - Copy and paste the contents of `supabase_tables.sql`
   - Click "Run" to create the tables

### Step 2: Test Your Connection

Run the test script to verify everything is working:

```bash
python test_supabase_connection.py
```

Expected output:
```
🧪 Testing data insertion...
✅ Successfully inserted 2 test records!
📖 Testing data reading...
✅ Successfully read 2 records!
🧹 Cleaning up test data...
✅ Test data cleaned up!
🎉 All tests passed! Your Supabase connection is working correctly.
```

### Step 3: Run Your Scrapers

Now you can run your scrapers and data will be stored in Supabase:

**For Singles Charts:**
```bash
python Project_music_charts/singles_scraper.py
```

**For Albums Charts:**
```bash
python Project_music_charts/albums_scraper.py
```

## 📊 Database Schema

### Singles Charts Table (`singles_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| single_title | TEXT | Song title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

### Albums Charts Table (`albums_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| album_title | TEXT | Album title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

## 🔍 Querying Your Data

### Example Queries

**Get top 10 singles for a specific date:**
```sql
SELECT single_title, artist, position 
FROM singles_charts 
WHERE chart_date = '2025-01-01' 
ORDER BY position 
LIMIT 10;
```

**Get all #1 hits by an artist:**
```sql
SELECT single_title, chart_date 
FROM singles_charts 
WHERE artist = 'Taylor Swift' AND position = 1 
ORDER BY chart_date DESC;
```

**Get chart statistics:**
```sql
SELECT * FROM get_chart_stats();
```

## 🛠️ Features

### ✅ What's Working
- **Automatic data insertion** to Supabase during scraping
- **Local backup** - Data is still saved to JSON files
- **Duplicate prevention** - Unique constraints prevent duplicate entries
- **Error handling** - Continues scraping even if Supabase fails
- **Progress tracking** - Resume scraping from where you left off

### 🔄 Data Flow
1. Scraper extracts data from website
2. Data is pushed to Supabase database
3. Data is also saved locally as JSON backup
4. Progress is tracked and can be resumed

## 🚨 Troubleshooting

### Common Issues

**1. Connection Failed**
```
❌ Supabase connection test failed
```
- Check your SUPABASE_URL and SUPABASE_KEY in `.env`
- Verify your Supabase project is active
- Make sure you ran the SQL script to create tables

**2. Insert Error**
```
❌ Supabase insert error: relation "singles_charts" does not exist
```
- Run the `supabase_tables.sql` script in your Supabase SQL Editor

**3. Permission Error**
```
❌ Error: insufficient_privilege
```
- Check your Supabase API key has the correct permissions
- Make sure you're using the service role key for server-side operations

### 🔧 Manual Table Creation

If the SQL script doesn't work, create tables manually:

1. Go to Supabase Dashboard → Table Editor
2. Click "Create a new table"
3. Use the schema from this guide

## 📈 Monitoring

### View Your Data
- **Supabase Dashboard**: Table Editor → View your tables
- **Local Files**: Check JSON files in `singles_charts_data/` and `albums_charts_data/`

### Statistics
Run this query to see your data stats:
```sql
SELECT 
  'singles' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM singles_charts
UNION ALL
SELECT 
  'albums' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM albums_charts;
```

## 🎯 Next Steps

1. **Run the test script** to verify setup
2. **Start scraping** with your updated scrapers
3. **Monitor progress** in Supabase dashboard
4. **Query your data** for analysis

Your music charts data will now be stored in a professional database with full SQL capabilities! 🎉
