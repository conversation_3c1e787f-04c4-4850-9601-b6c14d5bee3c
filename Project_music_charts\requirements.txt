# Music Charts Scraper - Requirements
# Install with: pip install -r requirements.txt

# Core scraping dependencies
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Database integration
supabase>=2.0.0
python-dotenv>=1.0.0

# Data handling
pandas>=2.0.0
openpyxl>=3.1.0

# Web scraping utilities
selenium>=4.15.0
html2text>=2020.1.16
readability-lxml>=0.8.1

# Development and utilities
pydantic>=2.0.0
tiktoken>=0.5.0

# Optional: For advanced features
streamlit>=1.28.0
streamlit-tags>=1.2.8

# API integrations
openai>=1.0.0
