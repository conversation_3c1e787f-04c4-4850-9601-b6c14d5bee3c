#!/usr/bin/env python3
"""
Supabase Database Setup Script
This script creates the necessary tables for storing music charts data.
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables.")
    sys.exit(1)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def create_singles_charts_table():
    """Create the singles_charts table"""
    
    # SQL to create the singles_charts table
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS singles_charts (
        id BIGSERIAL PRIMARY KEY,
        single_title TEXT NOT NULL,
        artist TEXT NOT NULL,
        chart_date DATE NOT NULL,
        position INTEGER NOT NULL,
        url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_singles_charts_chart_date ON singles_charts(chart_date);
    CREATE INDEX IF NOT EXISTS idx_singles_charts_artist ON singles_charts(artist);
    CREATE INDEX IF NOT EXISTS idx_singles_charts_position ON singles_charts(position);
    CREATE INDEX IF NOT EXISTS idx_singles_charts_title ON singles_charts(single_title);
    
    -- Create a unique constraint to prevent duplicate entries
    CREATE UNIQUE INDEX IF NOT EXISTS idx_singles_charts_unique 
    ON singles_charts(single_title, artist, chart_date, position);
    """
    
    try:
        # Execute the SQL using Supabase RPC (Remote Procedure Call)
        result = supabase.rpc('exec_sql', {'sql': create_table_sql}).execute()
        print("✅ Singles charts table created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating singles charts table: {e}")
        print("\nNote: If you get a 'function exec_sql does not exist' error,")
        print("you need to create this function in your Supabase SQL editor:")
        print("""
CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS void AS $$
BEGIN
    EXECUTE sql;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
        """)
        return False

def create_albums_charts_table():
    """Create the albums_charts table"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS albums_charts (
        id BIGSERIAL PRIMARY KEY,
        album_title TEXT NOT NULL,
        artist TEXT NOT NULL,
        chart_date DATE NOT NULL,
        position INTEGER NOT NULL,
        url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_albums_charts_chart_date ON albums_charts(chart_date);
    CREATE INDEX IF NOT EXISTS idx_albums_charts_artist ON albums_charts(artist);
    CREATE INDEX IF NOT EXISTS idx_albums_charts_position ON albums_charts(position);
    CREATE INDEX IF NOT EXISTS idx_albums_charts_title ON albums_charts(album_title);
    
    -- Create a unique constraint to prevent duplicate entries
    CREATE UNIQUE INDEX IF NOT EXISTS idx_albums_charts_unique 
    ON albums_charts(album_title, artist, chart_date, position);
    """
    
    try:
        result = supabase.rpc('exec_sql', {'sql': create_table_sql}).execute()
        print("✅ Albums charts table created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating albums charts table: {e}")
        return False

def test_connection():
    """Test the Supabase connection"""
    try:
        # Try to fetch from a system table to test connection
        result = supabase.table('singles_charts').select('*').limit(1).execute()
        print("✅ Supabase connection test successful!")
        return True
    except Exception as e:
        print(f"❌ Supabase connection test failed: {e}")
        return False

def main():
    print("🚀 Setting up Supabase database tables...")
    print(f"📡 Connecting to: {SUPABASE_URL[:30]}...")
    
    # Test connection first
    if not test_connection():
        print("❌ Cannot connect to Supabase. Please check your credentials.")
        return
    
    print("\n📊 Creating database tables...")
    
    # Create tables
    singles_success = create_singles_charts_table()
    albums_success = create_albums_charts_table()
    
    if singles_success and albums_success:
        print("\n🎉 Database setup completed successfully!")
        print("\nYou can now run your scrapers to store data in Supabase.")
    else:
        print("\n⚠️  Some tables could not be created. Please check the errors above.")
        print("\nYou may need to create the exec_sql function manually in Supabase.")

if __name__ == "__main__":
    main()
