#!/usr/bin/env python3
"""
Music Charts Scraper Setup Script
Installs dependencies and verifies the setup
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install all required packages"""
    print("🚀 Installing required packages...")
    
    try:
        # Get the directory where this script is located
        script_dir = Path(__file__).parent
        requirements_file = script_dir / "requirements.txt"
        
        if not requirements_file.exists():
            print("❌ requirements.txt file not found!")
            return False
        
        # Install packages
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All packages installed successfully!")
            return True
        else:
            print(f"❌ Error installing packages: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error during installation: {e}")
        return False

def check_env_file():
    """Check if .env file exists with required variables"""
    print("\n🔍 Checking environment variables...")
    
    # Look for .env file in parent directory (where it should be)
    env_file = Path(__file__).parent.parent / ".env"
    
    if not env_file.exists():
        print("❌ .env file not found in parent directory!")
        print("Please create a .env file with:")
        print("SUPABASE_URL=your_supabase_url")
        print("SUPABASE_KEY=your_supabase_key")
        return False
    
    # Check if required variables exist
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        has_url = "SUPABASE_URL=" in content
        has_key = "SUPABASE_KEY=" in content
        
        if has_url and has_key:
            print("✅ .env file found with required variables!")
            return True
        else:
            print("❌ .env file missing required variables!")
            if not has_url:
                print("  - Missing SUPABASE_URL")
            if not has_key:
                print("  - Missing SUPABASE_KEY")
            return False
            
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def verify_imports():
    """Verify that all required packages can be imported"""
    print("\n📦 Verifying package imports...")
    
    required_packages = [
        ("requests", "requests"),
        ("beautifulsoup4", "bs4"),
        ("supabase", "supabase"),
        ("python-dotenv", "dotenv"),
        ("pandas", "pandas"),
        ("selenium", "selenium"),
    ]
    
    failed_imports = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            print(f"  ❌ {package_name}")
            failed_imports.append(package_name)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ All packages imported successfully!")
        return True

def create_folders():
    """Create necessary folders for data storage"""
    print("\n📁 Creating data folders...")
    
    folders = [
        "singles_charts_data",
        "albums_charts_data",
        "musics_charts_datas"
    ]
    
    script_dir = Path(__file__).parent
    
    for folder in folders:
        folder_path = script_dir / folder
        if not folder_path.exists():
            folder_path.mkdir(exist_ok=True)
            print(f"  ✅ Created {folder}")
        else:
            print(f"  ✅ {folder} already exists")

def main():
    print("🎵 Music Charts Scraper Setup")
    print("=" * 50)
    
    # Step 1: Install requirements
    install_success = install_requirements()
    
    # Step 2: Check environment file
    env_success = check_env_file()
    
    # Step 3: Verify imports
    import_success = verify_imports()
    
    # Step 4: Create folders
    create_folders()
    
    # Summary
    print("\n" + "=" * 50)
    print("SETUP SUMMARY")
    print("=" * 50)
    
    if install_success and env_success and import_success:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Create Supabase tables using the SQL script")
        print("2. Run: python test_supabase_connection.py")
        print("3. Run: python singles_scraper.py")
        
        print("\nFiles ready:")
        print("  - requirements.txt ✅")
        print("  - .env file ✅")
        print("  - Data folders ✅")
        print("  - All packages ✅")
        
    else:
        print("⚠️  Setup completed with issues:")
        if not install_success:
            print("  - Package installation failed")
        if not env_success:
            print("  - Environment file issues")
        if not import_success:
            print("  - Package import issues")
        
        print("\nPlease fix the issues above before proceeding.")

if __name__ == "__main__":
    main()
