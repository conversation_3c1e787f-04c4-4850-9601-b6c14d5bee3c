# 🛠️ Manual Supabase Table Creation Guide

Since the automatic table creation isn't working, let's create the tables manually in your Supabase dashboard.

## 📋 Step-by-Step Instructions

### 1. Open Supabase Dashboard
- Go to https://supabase.com/dashboard
- Select your project

### 2. Create Singles Charts Table

1. **Go to Table Editor** (left sidebar)
2. **Click "Create a new table"**
3. **Table name:** `singles_charts`
4. **Add these columns:**

| Column Name | Type | Settings |
|-------------|------|----------|
| `id` | `int8` | ✅ Primary Key, ✅ Auto-increment |
| `single_title` | `text` | ✅ Required |
| `artist` | `text` | ✅ Required |
| `chart_date` | `date` | ✅ Required |
| `position` | `int4` | ✅ Required |
| `url` | `text` | Optional |
| `created_at` | `timestamptz` | Default: `now()` |
| `updated_at` | `timestamptz` | Default: `now()` |

5. **Click "Save"**

### 3. Create Albums Charts Table

1. **Click "Create a new table"** again
2. **Table name:** `albums_charts`
3. **Add these columns:**

| Column Name | Type | Settings |
|-------------|------|----------|
| `id` | `int8` | ✅ Primary Key, ✅ Auto-increment |
| `album_title` | `text` | ✅ Required |
| `artist` | `text` | ✅ Required |
| `chart_date` | `date` | ✅ Required |
| `position` | `int4` | ✅ Required |
| `url` | `text` | Optional |
| `created_at` | `timestamptz` | Default: `now()` |
| `updated_at` | `timestamptz` | Default: `now()` |

4. **Click "Save"**

## 🔧 Alternative: SQL Method

If you prefer SQL, go to **SQL Editor** and run this:

```sql
-- Create singles_charts table
CREATE TABLE singles_charts (
    id BIGSERIAL PRIMARY KEY,
    single_title TEXT NOT NULL,
    artist TEXT NOT NULL,
    chart_date DATE NOT NULL,
    position INTEGER NOT NULL,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create albums_charts table
CREATE TABLE albums_charts (
    id BIGSERIAL PRIMARY KEY,
    album_title TEXT NOT NULL,
    artist TEXT NOT NULL,
    chart_date DATE NOT NULL,
    position INTEGER NOT NULL,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_singles_charts_chart_date ON singles_charts(chart_date);
CREATE INDEX idx_singles_charts_artist ON singles_charts(artist);
CREATE INDEX idx_singles_charts_position ON singles_charts(position);

CREATE INDEX idx_albums_charts_chart_date ON albums_charts(chart_date);
CREATE INDEX idx_albums_charts_artist ON albums_charts(artist);
CREATE INDEX idx_albums_charts_position ON albums_charts(position);

-- Create unique constraints to prevent duplicates
CREATE UNIQUE INDEX idx_singles_charts_unique 
ON singles_charts(single_title, artist, chart_date, position);

CREATE UNIQUE INDEX idx_albums_charts_unique 
ON albums_charts(album_title, artist, chart_date, position);
```

## ✅ Test Your Setup

After creating the tables, run this to test:

```bash
python create_supabase_tables.py
```

You should see:
```
✅ singles_charts table already exists!
✅ albums_charts table already exists!
🎉 All tests passed! Your Supabase setup is working correctly.
```

## 🚀 Run Your Scrapers

Once tables are created, your scrapers will work:

```bash
python Project_music_charts/singles_scraper.py
```

The improved code will now:
- ✅ Check if tables exist before inserting
- ✅ Insert data in small batches (10 records at a time)
- ✅ Validate data before inserting
- ✅ Continue scraping even if some batches fail
- ✅ Provide detailed error messages

## 🔍 Monitor Your Data

After scraping, check your data in Supabase:
1. Go to **Table Editor**
2. Click on `singles_charts` or `albums_charts`
3. View your scraped data!

You can also run queries in **SQL Editor**:
```sql
-- Count total records
SELECT COUNT(*) FROM singles_charts;

-- Get latest chart data
SELECT * FROM singles_charts 
ORDER BY chart_date DESC 
LIMIT 10;

-- Top artists by number of chart entries
SELECT artist, COUNT(*) as chart_entries 
FROM singles_charts 
GROUP BY artist 
ORDER BY chart_entries DESC 
LIMIT 10;
```
