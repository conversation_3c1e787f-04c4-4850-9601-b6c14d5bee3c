#!/usr/bin/env python3
"""
Create Supabase Tables using Python
This script creates the necessary tables for storing music charts data.
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables.")
    sys.exit(1)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def create_tables_via_api():
    """Create tables using Supabase API calls"""
    
    print("🚀 Creating tables via Supabase API...")
    
    # Try to create a simple test record first to see if table exists
    try:
        # Test if singles_charts table exists
        result = supabase.table("singles_charts").select("*").limit(1).execute()
        print("✅ singles_charts table already exists!")
    except Exception as e:
        print(f"❌ singles_charts table doesn't exist: {e}")
        print("Please create the table manually in Supabase dashboard.")
        return False
    
    try:
        # Test if albums_charts table exists  
        result = supabase.table("albums_charts").select("*").limit(1).execute()
        print("✅ albums_charts table already exists!")
    except Exception as e:
        print(f"❌ albums_charts table doesn't exist: {e}")
        print("Please create the table manually in Supabase dashboard.")
        return False
    
    return True

def test_insert_and_delete():
    """Test inserting and deleting data"""
    
    print("\n🧪 Testing data operations...")
    
    # Test data for singles
    test_single = {
        "single_title": "Test Song",
        "artist": "Test Artist", 
        "chart_date": "2025-01-01",
        "position": 1,
        "url": "https://example.com/test"
    }
    
    try:
        # Insert test record
        print("📤 Inserting test single...")
        result = supabase.table("singles_charts").insert(test_single).execute()
        
        if result.data:
            print("✅ Test single inserted successfully!")
            record_id = result.data[0]['id']
            
            # Delete test record
            print("🗑️ Cleaning up test single...")
            delete_result = supabase.table("singles_charts").delete().eq('id', record_id).execute()
            print("✅ Test single deleted successfully!")
            return True
        else:
            print("❌ Failed to insert test single")
            return False
            
    except Exception as e:
        print(f"❌ Error testing singles table: {e}")
        return False

def main():
    print("🎵 Supabase Tables Setup")
    print("=" * 50)
    
    # Check if tables exist
    tables_exist = create_tables_via_api()
    
    if not tables_exist:
        print("\n❌ Tables don't exist. Please create them manually:")
        print("\n1. Go to your Supabase Dashboard")
        print("2. Go to Table Editor")
        print("3. Create a new table called 'singles_charts' with these columns:")
        print("   - id: int8 (Primary Key, Auto-increment)")
        print("   - single_title: text")
        print("   - artist: text") 
        print("   - chart_date: date")
        print("   - position: int4")
        print("   - url: text")
        print("   - created_at: timestamptz (Default: now())")
        print("   - updated_at: timestamptz (Default: now())")
        print("\n4. Create another table called 'albums_charts' with these columns:")
        print("   - id: int8 (Primary Key, Auto-increment)")
        print("   - album_title: text")
        print("   - artist: text")
        print("   - chart_date: date") 
        print("   - position: int4")
        print("   - url: text")
        print("   - created_at: timestamptz (Default: now())")
        print("   - updated_at: timestamptz (Default: now())")
        return
    
    # Test operations
    test_success = test_insert_and_delete()
    
    if test_success:
        print("\n🎉 All tests passed! Your Supabase setup is working correctly.")
        print("✅ You can now run your music charts scrapers!")
    else:
        print("\n⚠️ Tests failed. Please check your table permissions.")

if __name__ == "__main__":
    main()
