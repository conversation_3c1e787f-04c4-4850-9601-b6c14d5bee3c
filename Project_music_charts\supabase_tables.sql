-- Create singles_charts table
CREATE TABLE IF NOT EXISTS singles_charts (
    id BIGSERIAL PRIMARY KEY,
    single_title TEXT NOT NULL,
    artist TEXT NOT NULL,
    chart_date DATE NOT NULL,
    position INTEGER NOT NULL,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create albums_charts table
CREATE TABLE IF NOT EXISTS albums_charts (
    id BIGSERIAL PRIMARY KEY,
    album_title TEXT NOT NULL,
    artist TEXT NOT NULL,
    chart_date DATE NOT NULL,
    position INTEGER NOT NULL,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance on singles_charts
CREATE INDEX IF NOT EXISTS idx_singles_charts_chart_date ON singles_charts(chart_date);
CREATE INDEX IF NOT EXISTS idx_singles_charts_artist ON singles_charts(artist);
CREATE INDEX IF NOT EXISTS idx_singles_charts_position ON singles_charts(position);
CREATE INDEX IF NOT EXISTS idx_singles_charts_title ON singles_charts(single_title);

-- Create indexes for better performance on albums_charts
CREATE INDEX IF NOT EXISTS idx_albums_charts_chart_date ON albums_charts(chart_date);
CREATE INDEX IF NOT EXISTS idx_albums_charts_artist ON albums_charts(artist);
CREATE INDEX IF NOT EXISTS idx_albums_charts_position ON albums_charts(position);
CREATE INDEX IF NOT EXISTS idx_albums_charts_title ON albums_charts(album_title);

-- Create unique constraints to prevent duplicate entries
CREATE UNIQUE INDEX IF NOT EXISTS idx_singles_charts_unique 
ON singles_charts(single_title, artist, chart_date, position);

CREATE UNIQUE INDEX IF NOT EXISTS idx_albums_charts_unique 
ON albums_charts(album_title, artist, chart_date, position);

-- Enable Row Level Security (RLS) for better security
ALTER TABLE singles_charts ENABLE ROW LEVEL SECURITY;
ALTER TABLE albums_charts ENABLE ROW LEVEL SECURITY;

-- Create policies to allow authenticated users to read and write
CREATE POLICY "Allow authenticated users to read singles_charts" ON singles_charts
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert singles_charts" ON singles_charts
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to read albums_charts" ON albums_charts
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert albums_charts" ON albums_charts
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Create a function to get chart statistics
CREATE OR REPLACE FUNCTION get_chart_stats()
RETURNS TABLE(
    table_name TEXT,
    total_records BIGINT,
    unique_artists BIGINT,
    date_range_start DATE,
    date_range_end DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'singles_charts'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(DISTINCT artist)::BIGINT,
        MIN(chart_date)::DATE,
        MAX(chart_date)::DATE
    FROM singles_charts
    UNION ALL
    SELECT 
        'albums_charts'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(DISTINCT artist)::BIGINT,
        MIN(chart_date)::DATE,
        MAX(chart_date)::DATE
    FROM albums_charts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;



-------------------------------------------------
-------------------------------------------------
-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Allow authenticated users to read singles_charts" ON singles_charts;
DROP POLICY IF EXISTS "Allow authenticated users to insert singles_charts" ON singles_charts;
DROP POLICY IF EXISTS "Allow authenticated users to read albums_charts" ON albums_charts;
DROP POLICY IF EXISTS "Allow authenticated users to insert albums_charts" ON albums_charts;

-- Create more permissive policies that allow API key access
CREATE POLICY "Allow all operations on singles_charts" ON singles_charts
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on albums_charts" ON albums_charts
    FOR ALL USING (true) WITH CHECK (true);