import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime
import sys
import os
from urllib.parse import urljoin

from dotenv import load_dotenv
load_dotenv()

from supabase import create_client, Client

# Load Supabase environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

print(f"Loaded SUPABASE_URL: {SUPABASE_URL[:20]}..." if SUPABASE_URL else "SUPABASE_URL not set")
print(f"Loaded SUPABASE_KEY: {'set' if SUPABASE_KEY else 'not set'}")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables before running.")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class SinglesChartsScraper:
    def __init__(self):
        self.base_url = "https://musicchartsarchive.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        self.all_dates = {'singles_charts': {}}
        self.singles_data = []
        self.progress_tracker = {
            'completed_dates': [],
            'last_processed': None,
            'statistics': {
                'completed': 0,
                'total': 0,
                'start_time': None,
                'last_update': None
            }
        }

        self.folder_name = "singles_charts_data"
        self.dates_file = "D:/metademy_ai/scaper/Project_music_charts/musics_charts_datas/singles_charts_dates.json"
        self.progress_file = os.path.join(self.folder_name, "singles_scraping_progress.json")
        self.data_file = os.path.join(self.folder_name, "singles_charts_data.json")

        self.create_folder()

    def create_folder(self):
        try:
            if not os.path.exists(self.folder_name):
                os.makedirs(self.folder_name)
                print(f"Created folder: {self.folder_name}")
            else:
                print(f"Using existing folder: {self.folder_name}")
        except Exception as e:
            print(f"Error creating folder: {e}")

    def get_page(self, url, retries=3):
        for attempt in range(retries):
            try:
                print(f"Fetching: {url}")
                response = self.session.get(url, timeout=20)
                response.raise_for_status()
                return response.text
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:
                    time.sleep(3)
                else:
                    print(f"Failed after {retries} attempts")
                    return None

    def load_dates(self):
        try:
            with open(self.dates_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.all_dates['singles_charts'] = data.get('singles_charts', {})

            total = sum(len(dates) for decade_data in self.all_dates['singles_charts'].values()
                        for dates in decade_data.values())

            self.progress_tracker['statistics']['total'] = total

            print(f"Loaded singles dates from {self.dates_file}")
            print(f"Total Singles dates to process: {total}")
            return True
        except FileNotFoundError:
            print(f"File {self.dates_file} not found.")
            print("Please run the date collection script first!")
            return False
        except Exception as e:
            print(f"Error loading dates: {e}")
            return False

    def load_progress(self):
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                saved_progress = json.load(f)
                self.progress_tracker.update(saved_progress)
            print(f"Loaded singles progress from {self.progress_file}")
            self.print_progress_summary()
            return True
        except FileNotFoundError:
            print(f"No previous singles progress found. Starting fresh.")
            return False
        except Exception as e:
            print(f"Error loading progress: {e}")
            return False

    def save_progress(self):
        try:
            self.progress_tracker['statistics']['last_update'] = datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_tracker, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving progress: {e}")

    def load_existing_data(self):
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.singles_data = json.load(f)
            print(f"Loaded {len(self.singles_data)} existing singles records")
            return True
        except FileNotFoundError:
            print("No existing singles data file found. Starting with empty dataset.")
            return False
        except Exception as e:
            print(f"Error loading existing data: {e}")
            return False

    def is_date_completed(self, date_str):
        return date_str in self.progress_tracker['completed_dates']

    def mark_date_completed(self, date_str):
        if date_str not in self.progress_tracker['completed_dates']:
            self.progress_tracker['completed_dates'].append(date_str)
            self.progress_tracker['statistics']['completed'] += 1

        self.progress_tracker['last_processed'] = date_str
        self.save_progress()

    def print_progress_summary(self):
        stats = self.progress_tracker['statistics']
        print(f"\n{'=' * 50}")
        print("SINGLES SCRAPING PROGRESS")
        print('=' * 50)
        print(f"Completed: {stats['completed']}/{stats['total']}")

        if stats['total'] > 0:
            percent = (stats['completed'] / stats['total']) * 100
            print(f"Progress: {percent:.1f}%")

        if stats['start_time']:
            start_time = datetime.fromisoformat(stats['start_time'])
            elapsed = datetime.now() - start_time
            print(f"Time elapsed: {elapsed}")

        print(f"Last processed: {self.progress_tracker['last_processed']}")
        print(f"Current dataset size: {len(self.singles_data)} records")

    def get_remaining_dates(self):
        completed_dates = set(self.progress_tracker['completed_dates'])
        remaining_dates = []

        for decade, decade_data in self.all_dates['singles_charts'].items():
            for year, dates in decade_data.items():
                for date_str in dates:
                    if date_str not in completed_dates:
                        remaining_dates.append((decade, year, date_str))

        return remaining_dates

    def date_to_url_format(self, date_str):
        try:
            date_obj = datetime.strptime(date_str, "%b %d, %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            print(f"Error parsing date {date_str}: {e}")
            return None

    def clean_title_text(self, text):
        if not text:
            return ""
        cleaned = text.strip().strip('"').strip("'").strip()
        if cleaned.endswith(' Song'):
            cleaned = cleaned[:-5]

        cleaned = cleaned.replace('\\"', '"')
        cleaned = cleaned.replace("\\'", "'")
        cleaned = cleaned.replace('\\', '')
        cleaned = ' '.join(cleaned.split())

        return cleaned

    def clean_artist_text(self, text):
        if not text:
            return ""
        if ' | Music Charts Archive' in text:
            cleaned = text.replace(' | Music Charts Archive', '')
        else:
            cleaned = text
        return cleaned.strip()

    def extract_singles_entries(self, html_content, chart_date):
        if not html_content:
            return []

        soup = BeautifulSoup(html_content, 'html.parser')
        entries = []

        try:
            links = soup.find_all('a', href=True)
            position = 1

            for link in links:
                href = link.get('href', '')
                text = link.get_text(strip=True)

                if '/singles/' not in href:
                    continue

                if not text or len(text) < 3:
                    continue

                title = ""
                artist = ""

                if ' - ' in text:
                    parts = text.split(' - ', 1)
                    title = parts[0].strip()
                    artist = parts[1].strip()
                elif ' by ' in text:
                    parts = text.split(' by ', 1)
                    title = parts[0].strip()
                    artist = parts[1].strip()
                else:
                    title = text
                    if href:
                        url_parts = href.strip('/').split('/')
                        if len(url_parts) >= 3:
                            artist_slug = url_parts[1]
                            artist = artist_slug.replace('-', ' ').title()

                title = self.clean_title_text(title)
                artist = self.clean_artist_text(artist)

                if not title or len(title) < 2:
                    continue

                full_url = urljoin(self.base_url, href) if not href.startswith('http') else href

                entry = {
                    "Single Title": title,
                    "Artist": artist,
                    "Chart Date": chart_date,
                    "Position": position,
                    "URL": full_url
                }

                entries.append(entry)
                position += 1

                if position > 100:
                    break

        except Exception as e:
            print(f"Error extracting entries for {chart_date}: {e}")

        return entries

    def push_data_to_supabase(self, entries):
        try:
            rows = []
            for entry in entries:
                # Ensure chart_date is ISO date string
                chart_date_obj = datetime.strptime(entry.get("Chart Date"), "%Y-%m-%d")
                chart_date_str = chart_date_obj.date().isoformat()

                rows.append({
                    "single_title": entry.get("Single Title"),
                    "artist": entry.get("Artist"),
                    "chart_date": chart_date_str,
                    "position": entry.get("Position"),
                    "url": entry.get("URL"),
                })

            print(f"Attempting to insert {len(rows)} rows into Supabase:")
            print(json.dumps(rows, indent=2))

            response = supabase.table("singles_charts").insert(rows).execute()
            print("Supabase response:", response)

            if response.error:
                print(f"Supabase insert error: {response.error.message}")
                return False
            else:
                print(f"Pushed {len(rows)} entries to Supabase")
                return True
        except Exception as e:
            print(f"Exception while pushing to Supabase: {e}")
            return False

    def scrape_single_date(self, date_str):
        if self.is_date_completed(date_str):
            print(f"Skipping {date_str} (already completed)")
            return True

        url_date = self.date_to_url_format(date_str)
        if not url_date:
            return False

        chart_url = f"{self.base_url}/singles-chart/{url_date}"

        print(f"\nScraping singles for {date_str} ({url_date})")

        html_content = self.get_page(chart_url)
        entries = self.extract_singles_entries(html_content, url_date)

        if entries:
            print(f"Found {len(entries)} singles entries")

            success_push = self.push_data_to_supabase(entries)
            if success_push:
                self.mark_date_completed(date_str)
            else:
                print("Failed to push data to Supabase for this date.")
                # Mark completed anyway to avoid stuck state (optional)
                self.mark_date_completed(date_str)

            self.singles_data.extend(entries)  # keep local backup
            self.save_data()
            return True
        else:
            print(f"No singles entries found")
            self.mark_date_completed(date_str)
            return False

    def save_data(self):
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.singles_data, f, indent=2, ensure_ascii=False)
            print(f"Singles data saved ({len(self.singles_data)} records)")
        except Exception as e:
            print(f"Error saving data: {e}")

    def scrape_all_singles(self):
        if not self.progress_tracker['statistics']['start_time']:
            self.progress_tracker['statistics']['start_time'] = datetime.now().isoformat()

        print(f"\n{'=' * 60}")
        print("STARTING SINGLES CHARTS SCRAPING")
        print('=' * 60)

        try:
            remaining_dates = self.get_remaining_dates()

            if not remaining_dates:
                print("All singles dates already completed!")
                return

            print(f"Found {len(remaining_dates)} remaining singles dates to scrape")
            print("Processing order: 2025 → 2024 → 2023 → 2022 → 2021 → 2020...")
            print("This may take several hours...")
            print("Press Ctrl+C anytime to pause and resume later\n")

            for i, (decade, year, date_str) in enumerate(remaining_dates, 1):
                print(f"[{i}/{len(remaining_dates)}] Progress: {((i - 1) / len(remaining_dates) * 100):.1f}%")
                print(f"Decade: {decade}, Year: {year}")

                success = self.scrape_single_date(date_str)

                if success:
                    print(f"Completed {date_str}")
                else:
                    print(f"Failed {date_str}")

                if i % 10 == 0:
                    self.print_progress_summary()

                time.sleep(2)

            print(f"\nALL SINGLES CHARTS COMPLETED!")
            self.print_progress_summary()

            print(f"\nYour singles data folder: {self.folder_name}/")
            print(f"{os.path.basename(self.data_file)} - {len(self.singles_data)} singles records")
            print(f"{os.path.basename(self.progress_file)} - Progress tracking")
            print("Singles scraping completed successfully!")

        except KeyboardInterrupt:
            print(f"\n\nSINGLES SCRAPING PAUSED BY USER")
            self.print_progress_summary()
            print("\nSaving current progress...")
            self.save_data()
            self.save_progress()
            print("Progress saved! Run the script again to resume singles scraping.")
            return

        except Exception as e:
            print(f"\nUnexpected error: {e}")
            self.save_data()
            self.save_progress()
            return


def main():
    print("SINGLES CHARTS SCRAPER")
    print("=" * 50)
    print("Starting automatic singles scraping process...")

    scraper = SinglesChartsScraper()

    print("\nLoading singles chart dates...")
    if not scraper.load_dates():
        return

    print("\nChecking previous singles progress...")
    scraper.load_progress()

    print("\nLoading existing singles data...")
    scraper.load_existing_data()

    print("\n" + "=" * 50)
    print("SINGLES SCRAPING STATUS")
    print("=" * 50)
    scraper.print_progress_summary()

    if scraper.progress_tracker['statistics']['completed'] > 0:
        print(f"\nFound existing singles progress: {scraper.progress_tracker['statistics']['completed']} dates completed")
        confirm = input("Continue singles scraping from where we left off? (y/n): ").lower().strip()
        if confirm not in ['y', 'yes', '']:
            print("Singles scraping cancelled.")
            return
        print("Continuing singles scraping from previous progress...")
    else:
        print("\nStarting fresh singles scraping process...")

    scraper.scrape_all_singles()


if __name__ == "__main__":
    main()

