import os
import sys
import json
import time
import requests
from dotenv import load_dotenv
load_dotenv()
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin
from supabase import create_client, Client

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

print(f"Loaded SUPABASE_URL: {SUPABASE_URL[:20]}..." if SUPABASE_URL else "SUPABASE_URL not set")
print(f"Loaded SUPABASE_KEY: {'set' if SUPABASE_KEY else 'not set'}")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables before running.")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class AlbumsChartsScraper:
    def __init__(self):
        self.base_url = "https://musicchartsarchive.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        self.all_dates = {'album_charts': {}}
        self.albums_data = []
        self.progress_tracker = {
            'completed_dates': [],
            'last_processed': None,
            'statistics': {
                'completed': 0,
                'total': 0,
                'start_time': None,
                'last_update': None
            }
        }

        self.folder_name = "albums_charts_data"
        self.dates_file = "D:/metademy_ai/scaper/Project_music_charts/musics_charts_datas/albums_charts_datas.json"  # This stays in root
        self.progress_file = os.path.join(self.folder_name, "albums_scraping_progress.json")
        self.data_file = os.path.join(self.folder_name, "albums_charts_data.json")

        self.create_folder()
    
    def create_folder(self):
        """Create albums data folder if it doesn't exist"""
        try:
            if not os.path.exists(self.folder_name):
                os.makedirs(self.folder_name)
                print(f"Created folder: {self.folder_name}")
            else:
                print(f"Using existing folder: {self.folder_name}")
        except Exception as e:
            print(f"Error creating folder: {e}")
    
    def get_page(self, url, retries=3):
        """Get page content with retry mechanism"""
        for attempt in range(retries):
            try:
                print(f"Fetching: {url}")
                response = self.session.get(url, timeout=20)
                response.raise_for_status()
                return response.text
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:
                    time.sleep(3)
                else:
                    print(f"Failed after {retries} attempts")
                    return None
    
    def load_dates(self):
        """Load albums chart dates from JSON file"""
        try:
            with open(self.dates_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.all_dates['album_charts'] = data.get('album_charts', {})
            
            total = sum(len(dates) for decade_data in self.all_dates['album_charts'].values() 
                       for dates in decade_data.values())
            
            self.progress_tracker['statistics']['total'] = total
            
            print(f"Loaded albums dates from {self.dates_file}")
            print(f"Total Albums dates to process: {total}")
            return True
        except FileNotFoundError:
            print(f"File {self.dates_file} not found.")
            print("Please run the date collection script first!")
            return False
        except Exception as e:
            print(f"Error loading dates: {e}")
            return False
    
    def load_progress(self):
        """Load progress tracker from file"""
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                saved_progress = json.load(f)
                self.progress_tracker.update(saved_progress)
            print(f"Loaded albums progress from {self.progress_file}")
            self.print_progress_summary()
            return True
        except FileNotFoundError:
            print(f"No previous albums progress found. Starting fresh.")
            return False
        except Exception as e:
            print(f"Error loading progress: {e}")
            return False
    
    def save_progress(self):
        """Save progress tracker to file"""
        try:
            self.progress_tracker['statistics']['last_update'] = datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_tracker, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def load_existing_data(self):
        """Load existing scraped albums data"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.albums_data = json.load(f)
            print(f"Loaded {len(self.albums_data)} existing albums records")
            return True
        except FileNotFoundError:
            print("No existing albums data file found. Starting with empty dataset.")
            return False
        except Exception as e:
            print(f"Error loading existing data: {e}")
            return False
    
    def is_date_completed(self, date_str):
        """Check if a date has already been scraped"""
        return date_str in self.progress_tracker['completed_dates']
    
    def mark_date_completed(self, date_str):
        """Mark a date as completed"""
        if date_str not in self.progress_tracker['completed_dates']:
            self.progress_tracker['completed_dates'].append(date_str)
            self.progress_tracker['statistics']['completed'] += 1
        
        self.progress_tracker['last_processed'] = date_str
        self.save_progress()
    
    def print_progress_summary(self):
        """Print current progress summary"""
        stats = self.progress_tracker['statistics']
        print(f"\n{'='*50}")
        print("ALBUMS SCRAPING PROGRESS")
        print('='*50)
        print(f"Completed: {stats['completed']}/{stats['total']}")
        
        if stats['total'] > 0:
            percent = (stats['completed'] / stats['total']) * 100
            print(f"Progress: {percent:.1f}%")
        
        if stats['start_time']:
            start_time = datetime.fromisoformat(stats['start_time'])
            elapsed = datetime.now() - start_time
            print(f"Time elapsed: {elapsed}")
        
        print(f"Last processed: {self.progress_tracker['last_processed']}")
        print(f"Current dataset size: {len(self.albums_data)} records")
    
    def get_remaining_dates(self):
        """Get list of dates that haven't been scraped yet"""
        completed_dates = set(self.progress_tracker['completed_dates'])
        remaining_dates = []
        
        for decade, decade_data in self.all_dates['album_charts'].items():
            for year, dates in decade_data.items():
                for date_str in dates:
                    if date_str not in completed_dates:
                        remaining_dates.append((decade, year, date_str))
        
        return remaining_dates
    
    def date_to_url_format(self, date_str):
        """Convert 'Jan 4, 2025' to '2025-01-04' format"""
        try:
            date_obj = datetime.strptime(date_str, "%b %d, %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            print(f"Error parsing date {date_str}: {e}")
            return None
    
    def clean_title_text(self, text):
        """Clean title text and remove unwanted characters"""
        if not text:
            return ""
        cleaned = text.strip().strip('"').strip("'").strip()
        if cleaned.endswith(' Album'):
            cleaned = cleaned[:-6]

        cleaned = cleaned.replace('\\"', '"')
        cleaned = cleaned.replace("\\'", "'") 
        cleaned = cleaned.replace('\\', '')
        cleaned = ' '.join(cleaned.split())
        
        return cleaned
    
    def clean_artist_text(self, text):
        """Enhanced artist text cleaning"""
        if not text:
            return ""

        if ' | Music Charts Archive' in text:
            text = text.replace(' | Music Charts Archive', '')

        text = text.strip()

        if '-' in text and ' ' not in text:
            text = text.replace('-', ' ')

        if text.islower() or text.isupper():
            text = ' '.join(word.capitalize() for word in text.split())
        
        return text
    
    def extract_albums_entries(self, html_content, chart_date):
        """Extract albums chart entries from HTML in proper position order"""
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        entries = []
        
        try:
            album_links = soup.find_all('a', href=lambda x: x and '/albums/' in x)
            print(f"Found {len(album_links)} album links on page")

            positioned_entries = []
            
            for link in album_links:
                href = link.get('href', '')
                title_text = link.get_text(strip=True)
                
                if not title_text or len(title_text) < 2:
                    continue

                position = None
                current_element = link
                for _ in range(3):
                    parent = current_element.parent if current_element else None
                    if parent:
                        parent_text = parent.get_text()
                        position_matches = re.findall(r'\b(\d{1,3})\b', parent_text)
                        for match in position_matches:
                            pos_num = int(match)
                            if 1 <= pos_num <= 200:
                                position = pos_num
                                break
                        if position:
                            break
                        current_element = parent
                    else:
                        break

                if not position:
                    if link.parent:
                        siblings = link.parent.find_all(text=True)
                        for sibling in siblings:
                            sibling_text = sibling.strip()
                            if sibling_text.isdigit():
                                pos_num = int(sibling_text)
                                if 1 <= pos_num <= 200:
                                    position = pos_num
                                    break

                artist = ""
                if href:
                    url_parts = href.strip('/').split('/')
                    if len(url_parts) >= 2:
                        artist_slug = url_parts[1] if url_parts[0] == 'albums' else url_parts[-2]
                        artist = artist_slug.replace('-', ' ').replace('_', ' ')
                        artist = ' '.join(word.capitalize() for word in artist.split())
                
                title = self.clean_title_text(title_text)
                artist = self.clean_artist_text(artist)
                
                if not title or len(title) < 2:
                    continue

                full_url = urljoin(self.base_url, href) if not href.startswith('http') else href

                positioned_entries.append({
                    'title': title,
                    'artist': artist if artist else "Unknown Artist",
                    'url': full_url,
                    'position': position,
                    'original_order': len(positioned_entries) + 1
                })

            if positioned_entries:
                entries_with_positions = [e for e in positioned_entries if e['position'] is not None]
                entries_without_positions = [e for e in positioned_entries if e['position'] is None]
                
                if entries_with_positions:
                    entries_with_positions.sort(key=lambda x: x['position'])

                    if len(entries_with_positions) >= len(positioned_entries) * 0.8:
                        final_entries = entries_with_positions
                        for entry in entries_without_positions:
                            entry['position'] = len(final_entries) + 1
                            final_entries.append(entry)
                    else:
                        final_entries = positioned_entries
                        for i, entry in enumerate(final_entries, 1):
                            entry['position'] = i
                else:
                    final_entries = positioned_entries
                    for i, entry in enumerate(final_entries, 1):
                        entry['position'] = i

                for entry_data in final_entries:
                    entry = {
                        "Album Title": entry_data['title'],
                        "Artist": entry_data['artist'],
                        "Chart Date": chart_date,
                        "Position": entry_data['position'],
                        "URL": entry_data['url']
                    }
                    entries.append(entry)
            
            print(f"Extracted {len(entries)} entries with positions")
            
        except Exception as e:
            print(f"Error extracting entries for {chart_date}: {e}")
        
        if entries:
            entries.sort(key=lambda x: x['Position'])
        
        return entries

    def push_data_to_supabase(self, entries):
        """Push albums data to Supabase database"""
        try:
            # First, check if table exists
            try:
                supabase.table("albums_charts").select("id").limit(1).execute()
                print("Table 'albums_charts' exists and is accessible")
            except Exception as table_error:
                print(f"Table access error: {table_error}")
                print("Please create the 'albums_charts' table first using the setup script.")
                return False

            rows = []
            for entry in entries:
                try:
                    chart_date_obj = datetime.strptime(entry.get("Chart Date"), "%Y-%m-%d")
                    chart_date_str = chart_date_obj.date().isoformat()

                    album_title = entry.get("Album Title", "").strip()
                    artist = entry.get("Artist", "").strip()
                    position = entry.get("Position")
                    url = entry.get("URL", "").strip()

                    if not album_title or not artist or position is None:
                        print(f"Skipping invalid entry: {entry}")
                        continue

                    rows.append({
                        "album_title": album_title,
                        "artist": artist,
                        "chart_date": chart_date_str,
                        "position": int(position),
                        "url": url,
                    })
                except Exception as entry_error:
                    print(f"Error processing entry {entry}: {entry_error}")
                    continue

            if not rows:
                print("No valid rows to insert")
                return False
            print(f"Attempting to insert {len(rows)} albums rows into Supabase...")

            batch_size = 10
            total_inserted = 0

            for i in range(0, len(rows), batch_size):
                batch = rows[i:i + batch_size]
                try:
                    response = supabase.table("albums_charts").insert(batch).execute()

                    if hasattr(response, 'data') and response.data:
                        batch_inserted = len(response.data)
                        total_inserted += batch_inserted
                        print(f"Batch {i//batch_size + 1}: Inserted {batch_inserted} records")
                    else:
                        print(f"Batch {i//batch_size + 1}: No data returned, but no error")

                except Exception as batch_error:
                    print(f"Batch {i//batch_size + 1} failed: {batch_error}")
                    continue

            if total_inserted > 0:
                print(f"Successfully pushed {total_inserted}/{len(rows)} albums entries to Supabase")
                return True
            else:
                print("No albums entries were successfully inserted")
                return False

        except Exception as e:
            print(f"Exception while pushing albums to Supabase: {e}")
            print(f"Error type: {type(e).__name__}")
            return False

    def scrape_single_date(self, date_str):
        """Scrape albums data for a single date"""
        if self.is_date_completed(date_str):
            print(f"Skipping {date_str} (already completed)")
            return True
        
        url_date = self.date_to_url_format(date_str)
        if not url_date:
            return False

        chart_url = f"{self.base_url}/album-chart/{url_date}"
        print(f"\nScraping albums for {date_str} ({url_date})")
        
        html_content = self.get_page(chart_url)
        entries = self.extract_albums_entries(html_content, url_date)
        
        if entries:
            print(f"Found {len(entries)} albums entries")

            success_push = self.push_data_to_supabase(entries)
            if success_push:
                self.mark_date_completed(date_str)
            else:
                print("Failed to push albums data to Supabase for this date.")
                self.mark_date_completed(date_str)

            self.albums_data.extend(entries)
            self.save_data()
            return True
        else:
            print(f"No albums entries found")
            self.mark_date_completed(date_str)
            return False
    
    def save_data(self):
        """Save albums data to file with proper position ordering"""
        sorted_data = sorted(self.albums_data, key=lambda x: (x['Chart Date'], x['Position']), reverse=False)

        data_by_date = {}
        for entry in sorted_data:
            date = entry['Chart Date']
            if date not in data_by_date:
                data_by_date[date] = []
            data_by_date[date].append(entry)

        for date in data_by_date:
            data_by_date[date].sort(key=lambda x: x['Position'])

        final_sorted_data = []
        for date in sorted(data_by_date.keys(), reverse=True):
            final_sorted_data.extend(data_by_date[date])
        
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(final_sorted_data, f, indent=2, ensure_ascii=False)
        
        print(f"Albums data saved ({len(self.albums_data)} records)")
    
    def print_progress_summary(self):
        """Print current progress summary with position info"""
        stats = self.progress_tracker['statistics']
        print(f"\n{'='*50}")
        print("ALBUMS SCRAPING PROGRESS")
        print('='*50)
        print(f"Completed: {stats['completed']}/{stats['total']}")
        
        if stats['total'] > 0:
            percent = (stats['completed'] / stats['total']) * 100
            print(f"Progress: {percent:.1f}%")
        
        if stats['start_time']:
            start_time = datetime.fromisoformat(stats['start_time'])
            elapsed = datetime.now() - start_time
            print(f"Time elapsed: {elapsed}")
        
        print(f"Last processed: {self.progress_tracker['last_processed']}")
        print(f"Current dataset size: {len(self.albums_data)} records")
        
        if self.albums_data:
            positions = [entry['Position'] for entry in self.albums_data if 'Position' in entry]
            if positions:
                print(f"Position range: {min(positions)} to {max(positions)}")
                print(f"Charts with Position 1: {positions.count(1)}")
    
    def scrape_all_albums(self):
        """Main albums scraping method"""
        if not self.progress_tracker['statistics']['start_time']:
            self.progress_tracker['statistics']['start_time'] = datetime.now().isoformat()
        
        print(f"\n{'='*60}")
        print("STARTING ALBUMS CHARTS SCRAPING")
        print('='*60)
        
        try:
            remaining_dates = self.get_remaining_dates()
            
            if not remaining_dates:
                print(" All albums dates already completed!")
                return
            
            print(f"Found {len(remaining_dates)} remaining albums dates to scrape")
            print(" Processing order: 2025 → 2024 → 2023 → 2022 → 2021 → 2020...")
            print(" This may take several hours...")
            print(" Press Ctrl+C anytime to pause and resume later\n")
            
            for i, (decade, year, date_str) in enumerate(remaining_dates, 1):
                print(f"[{i}/{len(remaining_dates)}] Progress: {((i-1)/len(remaining_dates)*100):.1f}%")
                print(f"Decade: {decade}, Year: {year}")
                
                success = self.scrape_single_date(date_str)
                
                if success:
                    print(f"Completed {date_str}")
                else:
                    print(f" Failed {date_str}")
                
                if i % 10 == 0:
                    self.print_progress_summary()
                
                time.sleep(2)
            
            print(f"\n ALL ALBUMS CHARTS COMPLETED!")
            self.print_progress_summary()
            
            print(f"\n Your albums data folder: {self.folder_name}/")
            print(f" {os.path.basename(self.data_file)} - {len(self.albums_data)} albums records")
            print(f" {os.path.basename(self.progress_file)} - Progress tracking")
            print(" Albums scraping completed successfully!")
        
        except KeyboardInterrupt:
            print(f"\n\n ALBUMS SCRAPING PAUSED BY USER")
            self.print_progress_summary()
            print("\nSaving current progress...")
            self.save_data()
            self.save_progress()
            print("Progress saved! Run the script again to resume albums scraping.")
            return
        
        except Exception as e:
            print(f"\n Unexpected error: {e}")
            self.save_data()
            self.save_progress()
            return

def main():
    print(" ALBUMS CHARTS SCRAPER ")
    print("=" * 50)
    print(" Starting automatic albums scraping process...")
    
    scraper = AlbumsChartsScraper()
    
    print("\n Loading albums chart dates...")
    if not scraper.load_dates():
        return
    
    print("\n Checking previous albums progress...")
    scraper.load_progress()
    
    print("\n Loading existing albums data...")
    scraper.load_existing_data()
    
    print("\n" + "="*50)
    print("ALBUMS SCRAPING STATUS")
    print("="*50)
    scraper.print_progress_summary()
 
    if scraper.progress_tracker['statistics']['completed'] > 0:
        print(f"\nFound existing albums progress: {scraper.progress_tracker['statistics']['completed']} dates completed")
        confirm = input("Continue albums scraping from where we left off? (y/n): ").lower().strip()
        if confirm not in ['y', 'yes', '']:
            print(" Albums scraping cancelled.")
            return
        print(" Continuing albums scraping from previous progress...")
    else:
        print("\n Starting fresh albums scraping process...")
    
    scraper.scrape_all_albums()

if __name__ == "__main__":
    main()