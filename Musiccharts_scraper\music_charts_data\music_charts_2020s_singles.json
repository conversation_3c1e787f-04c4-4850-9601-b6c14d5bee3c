[{"Single Title": "\"All I Want For Christmas Is You [seasonal]\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 1, "URL": "https://musicchartsarchive.com/singles/mariah-carey/all-i-want-for-christmas-is-you"}, {"Single Title": "\"Rockin' Around The Christmas Tree [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 2, "URL": "https://musicchartsarchive.com/singles/brenda-lee/rockin-around-the-christmas-tree"}, {"Single Title": "\"Jingle Bell Rock [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 3, "URL": "https://musicchartsarchive.com/singles/bobby-helms/jingle-bell-rock"}, {"Single Title": "\"Holly Jolly Christmas [seasonal]\" Song", "Artist": "<PERSON><PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 4, "URL": "https://musicchartsarchive.com/singles/burl-ives/holly-jolly-christmas"}, {"Single Title": "\"Circles\" Song", "Artist": "<PERSON> Malone | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 5, "URL": "https://musicchartsarchive.com/singles/post-malone/circles"}, {"Single Title": "\"<PERSON><PERSON><PERSON>\" Song", "Artist": "Arizona Zervas | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 6, "URL": "https://musicchartsarchive.com/singles/arizona-zervas/roxanne"}, {"Single Title": "\"It's The Most Wonderful Time Of The Year [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 7, "URL": "https://musicchartsarchive.com/singles/andy-williams/its-the-most-wonderful-time-of-the-year"}, {"Single Title": "\"Someone You Loved\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 8, "URL": "https://musicchartsarchive.com/singles/lewis-capaldi/someone-you-loved"}, {"Single Title": "\"Memories\" Song", "Artist": "Maroon 5 | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 9, "URL": "https://musicchartsarchive.com/singles/maroon-5/memories"}, {"Single Title": "\"Good As Hell\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 10, "URL": "https://musicchartsarchive.com/singles/lizzo/good-as-hell"}, {"Single Title": "\"Last Christmas [seasonal]\" Song", "Artist": "Wham! | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 11, "URL": "https://musicchartsarchive.com/singles/wham/last-christmas"}, {"Single Title": "\"<PERSON><PERSON><PERSON> [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 12, "URL": "https://musicchartsarchive.com/singles/jose-feliciano/feliz-navidad"}, {"Single Title": "\"The Box\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 13, "URL": "https://musicchartsarchive.com/singles/roddy-ricch/the-box"}, {"Single Title": "\"Dance Monkey\" Song", "Artist": "Tones And I | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 14, "URL": "https://musicchartsarchive.com/singles/tones-and-i/dance-monkey"}, {"Single Title": "\"Let It Snow, Let It Snow, Let It Snow [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 15, "URL": "https://musicchartsarchive.com/singles/dean-martin/let-it-snow-let-it-snow-let-it-snow"}, {"Single Title": "\"The Christmas Song [seasonal]\" Song", "Artist": "<PERSON> \"King\" <PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 16, "URL": "https://musicchartsarchive.com/singles/nat-king-cole/the-christmas-song"}, {"Single Title": "\"10,000 Hours\" Song", "Artist": "<PERSON> <PERSON> <PERSON> & <PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 17, "URL": "https://musicchartsarchive.com/singles/dan-and-shay/10000-hours"}, {"Single Title": "\"Lose You To Love Me\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 18, "URL": "https://musicchartsarchive.com/singles/selena-gomez/lose-you-to-love-me"}, {"Single Title": "\"Ballin'\" Song", "Artist": "<PERSON>ard feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 19, "URL": "https://musicchartsarchive.com/singles/dj-mustard/ballin"}, {"Single Title": "\"Bop\" Song", "Artist": "DaBaby | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 20, "URL": "https://musicchartsarchive.com/singles/dababy/bop"}, {"Single Title": "\"Sleigh Ride [seasonal]\" Song", "Artist": "The Ronettes | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 21, "URL": "https://musicchartsarchive.com/singles/ronettes/sleigh-ride"}, {"Single Title": "\"Rudolph The Red-Nosed Reindeer [seasonal]\" Song", "Artist": "<PERSON> Autry | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 22, "URL": "https://musicchartsarchive.com/singles/gene-autry/rudolph-the-red-nosed-reindeer"}, {"Single Title": "\"Bad Guy\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 23, "URL": "https://musicchartsarchive.com/singles/billie-eilish/bad-guy"}, {"Single Title": "\"Happy Holiday / The Holiday Season [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 24, "URL": "https://musicchartsarchive.com/singles/andy-williams/happy-holiday"}, {"Single Title": "\"No Guidance\" Song", "Artist": "<PERSON> & Drake | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 25, "URL": "https://musicchartsarchive.com/singles/chris-brown/no-guidance"}, {"Single Title": "\"Senorita\" Song", "Artist": "<PERSON> & Camila C<PERSON>llo | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 26, "URL": "https://musicchartsarchive.com/singles/shawn-mendes/senorita"}, {"Single Title": "\"Bandit\" Song", "Artist": "Juice WRLD & YoungBoy Never Broke Again | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 27, "URL": "https://musicchartsarchive.com/singles/juice-wrld/bandit"}, {"Single Title": "\"It's Beginning To Look A Lot Like Christmas [seasonal]\" Song", "Artist": "<PERSON> and the Fontane Sisters | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 28, "URL": "https://musicchartsarchive.com/singles/perry-como/its-beginning-to-look-a-lot-like-christmas"}, {"Single Title": "\"Christmas (Baby Please Come Home) [seasonal]\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 29, "URL": "https://musicchartsarchive.com/singles/darlene-love/christmas-baby-please-come-home"}, {"Single Title": "\"Panini\" Song", "Artist": "Lil Nas X | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 30, "URL": "https://musicchartsarchive.com/singles/lil-nas-x/panini"}, {"Single Title": "\"Underneath The Tree [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 31, "URL": "https://musicchartsarchive.com/singles/kelly-clarkson/underneath-the-tree-seasonal"}, {"Single Title": "\"Here Comes Santa Claus [seasonal]\" Song", "Artist": "<PERSON> Autry | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 32, "URL": "https://musicchartsarchive.com/singles/gene-autry/here-comes-santa-claus"}, {"Single Title": "\"Hot\" Song", "Artist": "Young Thug feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 33, "URL": "https://musicchartsarchive.com/singles/young-thug/hot"}, {"Single Title": "\"Old Town Road\" Song", "Artist": "Lil Nas X feat. <PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 34, "URL": "https://musicchartsarchive.com/singles/lil-nas-x/old-town-road"}, {"Single Title": "\"Truth Hurts\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 35, "URL": "https://musicchartsarchive.com/singles/lizzo/truth-hurts"}, {"Single Title": "\"Run Rudolph Run [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 36, "URL": "https://musicchartsarchive.com/singles/chuck-berry/run-rudolph-run"}, {"Single Title": "\"(There's No Place Like) Home For The Holidays (1959 Version)\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 37, "URL": "https://musicchartsarchive.com/singles/perry-como/home-for-the-holidays"}, {"Single Title": "\"Highest In The Room\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 38, "URL": "https://musicchartsarchive.com/singles/travis-scott/highest-in-the-room"}, {"Single Title": "\"Heartless\" Song", "Artist": "The Weeknd | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 39, "URL": "https://musicchartsarchive.com/singles/the-weeknd/heartless"}, {"Single Title": "\"Blue Christmas [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 40, "URL": "https://musicchartsarchive.com/singles/elvis-presley/blue-christmas"}, {"Single Title": "\"Everything I Wanted\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 41, "URL": "https://musicchartsarchive.com/singles/billie-eilish/everything-i-wanted"}, {"Single Title": "\"White Christmas [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 42, "URL": "https://musicchartsarchive.com/singles/bing-crosby/white-christmas"}, {"Single Title": "\"Jingle Bells [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 43, "URL": "https://musicchartsarchive.com/singles/frank-sinatra/jingle-bells"}, {"Single Title": "\"Like It's Christmas\" Song", "Artist": "Jonas Brothers | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 44, "URL": "https://musicchartsarchive.com/singles/jonas-brothers/like-its-christmas"}, {"Single Title": "\"Baby It's Cold Outside [seasonal]\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 45, "URL": "https://musicchartsarchive.com/singles/dean-martin/baby-its-cold-outside"}, {"Single Title": "\"Trampoline\" Song", "Artist": "Shaed | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 46, "URL": "https://musicchartsarchive.com/singles/shaed/trampoline"}, {"Single Title": "\"Woah\" Song", "Artist": "Lil Baby | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 47, "URL": "https://musicchartsarchive.com/singles/lil-baby/woah"}, {"Single Title": "\"Futsal Shuffle 2020\" Song", "Artist": "<PERSON> U<PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 48, "URL": "https://musicchartsarchive.com/singles/lil-uzi-vert/futsal-shuffle-2020"}, {"Single Title": "\"You're A Mean One, Mr<PERSON> <PERSON>rinch [seasonal]\" Song", "Artist": "<PERSON><PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 49, "URL": "https://musicchartsarchive.com/singles/thurl-ravenscroft/youre-a-mean-one-mr-grinch"}, {"Single Title": "\"Ran$om\" Song", "Artist": "Lil Tecca | Music Charts Archive", "Chart Date": "2020-01-04", "Position": 50, "URL": "https://musicchartsarchive.com/singles/lil-tecca/ransom"}, {"Single Title": "\"Circles\" Song", "Artist": "<PERSON> Malone | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 1, "URL": "https://musicchartsarchive.com/singles/post-malone/circles"}, {"Single Title": "\"Memories\" Song", "Artist": "Maroon 5 | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 2, "URL": "https://musicchartsarchive.com/singles/maroon-5/memories"}, {"Single Title": "\"The Box\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 3, "URL": "https://musicchartsarchive.com/singles/roddy-ricch/the-box"}, {"Single Title": "\"Someone You Loved\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 4, "URL": "https://musicchartsarchive.com/singles/lewis-capaldi/someone-you-loved"}, {"Single Title": "\"<PERSON><PERSON><PERSON>\" Song", "Artist": "Arizona Zervas | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 5, "URL": "https://musicchartsarchive.com/singles/arizona-zervas/roxanne"}, {"Single Title": "\"Good As Hell\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 6, "URL": "https://musicchartsarchive.com/singles/lizzo/good-as-hell"}, {"Single Title": "\"Dance Monkey\" Song", "Artist": "Tones And I | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 7, "URL": "https://musicchartsarchive.com/singles/tones-and-i/dance-monkey"}, {"Single Title": "\"Highest In The Room\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 8, "URL": "https://musicchartsarchive.com/singles/travis-scott/highest-in-the-room"}, {"Single Title": "\"10,000 Hours\" Song", "Artist": "<PERSON> <PERSON> <PERSON> & <PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 9, "URL": "https://musicchartsarchive.com/singles/dan-and-shay/10000-hours"}, {"Single Title": "\"Lose You To Love Me\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 10, "URL": "https://musicchartsarchive.com/singles/selena-gomez/lose-you-to-love-me"}, {"Single Title": "\"Senorita\" Song", "Artist": "<PERSON> & Camila C<PERSON>llo | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 11, "URL": "https://musicchartsarchive.com/singles/shawn-mendes/senorita"}, {"Single Title": "\"Ballin'\" Song", "Artist": "<PERSON>ard feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 12, "URL": "https://musicchartsarchive.com/singles/dj-mustard/ballin"}, {"Single Title": "\"Bop\" Song", "Artist": "DaBaby | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 13, "URL": "https://musicchartsarchive.com/singles/dababy/bop"}, {"Single Title": "\"Bad Guy\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 14, "URL": "https://musicchartsarchive.com/singles/billie-eilish/bad-guy"}, {"Single Title": "\"Old Town Road\" Song", "Artist": "Lil Nas X feat. <PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 15, "URL": "https://musicchartsarchive.com/singles/lil-nas-x/old-town-road"}, {"Single Title": "\"No Guidance\" Song", "Artist": "<PERSON> & Drake | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 16, "URL": "https://musicchartsarchive.com/singles/chris-brown/no-guidance"}, {"Single Title": "\"Panini\" Song", "Artist": "Lil Nas X | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 17, "URL": "https://musicchartsarchive.com/singles/lil-nas-x/panini"}, {"Single Title": "\"Truth Hurts\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 18, "URL": "https://musicchartsarchive.com/singles/lizzo/truth-hurts"}, {"Single Title": "\"Trampoline\" Song", "Artist": "Shaed | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 19, "URL": "https://musicchartsarchive.com/singles/shaed/trampoline"}, {"Single Title": "\"Heartless\" Song", "Artist": "The Weeknd | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 20, "URL": "https://musicchartsarchive.com/singles/the-weeknd/heartless"}, {"Single Title": "\"Don't Start Now\" Song", "Artist": "Dua Lipa | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 21, "URL": "https://musicchartsarchive.com/singles/dua-lipa/dont-start-now"}, {"Single Title": "\"Bandit\" Song", "Artist": "Juice WRLD & YoungBoy Never Broke Again | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 22, "URL": "https://musicchartsarchive.com/singles/juice-wrld/bandit"}, {"Single Title": "\"Everything I Wanted\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 23, "URL": "https://musicchartsarchive.com/singles/billie-eilish/everything-i-wanted"}, {"Single Title": "\"Hot\" Song", "Artist": "Young Thug feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 24, "URL": "https://musicchartsarchive.com/singles/young-thug/hot"}, {"Single Title": "\"Only Human\" Song", "Artist": "Jonas Brothers | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 25, "URL": "https://musicchartsarchive.com/singles/jonas-brothers/only-human"}, {"Single Title": "\"One Man Band\" Song", "Artist": "Old Dominion | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 26, "URL": "https://musicchartsarchive.com/singles/old-dominion/one-man-band"}, {"Single Title": "\"Woah\" Song", "Artist": "Lil Baby | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 27, "URL": "https://musicchartsarchive.com/singles/lil-baby/woah"}, {"Single Title": "\"I Don't Care\" Song", "Artist": "<PERSON> & <PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 28, "URL": "https://musicchartsarchive.com/singles/ed-sheeran/i-dont-care"}, {"Single Title": "\"Sucker\" Song", "Artist": "Jonas Brothers | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 29, "URL": "https://musicchartsarchive.com/singles/jonas-brothers/sucker"}, {"Single Title": "\"Even Though I'm Leaving\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 30, "URL": "https://musicchartsarchive.com/singles/luke-combs/even-though-im-leaving"}, {"Single Title": "\"Falling\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 31, "URL": "https://musicchartsarchive.com/singles/trevor-daniel/falling"}, {"Single Title": "\"Hot Girl Bummer\" Song", "Artist": "blackbear | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 32, "URL": "https://musicchartsarchive.com/singles/blackbear/hot-girl-bummer"}, {"Single Title": "\"Ran$om\" Song", "Artist": "Lil Tecca | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 33, "URL": "https://musicchartsarchive.com/singles/lil-tecca/ransom"}, {"Single Title": "\"Lover\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 34, "URL": "https://musicchartsarchive.com/singles/taylor-swift/lover"}, {"Single Title": "\"Adore You\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 35, "URL": "https://musicchartsarchive.com/singles/harry-styles/adore-you"}, {"Single Title": "\"The Bones\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 36, "URL": "https://musicchartsarchive.com/singles/maren-morris/the-bones"}, {"Single Title": "\"Graveyard\" Song", "Artist": "Halsey | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 37, "URL": "https://musicchartsarchive.com/singles/halsey/graveyard"}, {"Single Title": "\"Out West\" Song", "Artist": "<PERSON><PERSON><PERSON><PERSON> feat. <PERSON> Thug | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 38, "URL": "https://musicchartsarchive.com/singles/jackboys/out-west"}, {"Single Title": "\"On Chill\" Song", "Artist": "Wale feat Jeremih | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 39, "URL": "https://musicchartsarchive.com/singles/wale/on-chill"}, {"Single Title": "\"Talk\" Song", "Artist": "Khalid | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 40, "URL": "https://musicchartsarchive.com/singles/khalid/talk"}, {"Single Title": "\"Suicidal\" Song", "Artist": "YNW Melly | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 41, "URL": "https://musicchartsarchive.com/singles/ynw-melly/suicidal"}, {"Single Title": "\"Beautiful People\" Song", "Artist": "<PERSON> feat. <PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 42, "URL": "https://musicchartsarchive.com/singles/ed-sheeran/beautiful-people"}, {"Single Title": "\"Suge\" Song", "Artist": "DaBaby | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 43, "URL": "https://musicchartsarchive.com/singles/dababy/suge"}, {"Single Title": "\"Futsal Shuffle 2020\" Song", "Artist": "<PERSON> U<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 44, "URL": "https://musicchartsarchive.com/singles/lil-uzi-vert/futsal-shuffle-2020"}, {"Single Title": "\"Juicy\" Song", "Artist": "Doja Cat & Tyga | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 45, "URL": "https://musicchartsarchive.com/singles/doja-cat/juicy"}, {"Single Title": "\"No Idea\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 46, "URL": "https://musicchartsarchive.com/singles/don-toliver/no-idea"}, {"Single Title": "\"Baby\" Song", "Artist": "Lil Baby & DaBaby | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 47, "URL": "https://musicchartsarchive.com/singles/lil-baby/baby"}, {"Single Title": "\"Gang Gang\" Song", "Artist": "JackBoy<PERSON> & She<PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 48, "URL": "https://musicchartsarchive.com/singles/jackboys/gang-gang"}, {"Single Title": "\"What If I Never Get Over You\" Song", "Artist": "Lady Antebellum | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 49, "URL": "https://musicchartsarchive.com/singles/lady-antebellum/what-if-i-never-get-over-you"}, {"Single Title": "\"My Oh My\" Song", "Artist": "<PERSON><PERSON> feat. <PERSON><PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-11", "Position": 50, "URL": "https://musicchartsarchive.com/singles/camila-cabello/my-oh-my"}, {"Single Title": "\"The Box\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 1, "URL": "https://musicchartsarchive.com/singles/roddy-ricch/the-box"}, {"Single Title": "\"Yummy\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 2, "URL": "https://musicchartsarchive.com/singles/justin-bieber/yummy"}, {"Single Title": "\"Circles\" Song", "Artist": "<PERSON> Malone | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 3, "URL": "https://musicchartsarchive.com/singles/post-malone/circles"}, {"Single Title": "\"Memories\" Song", "Artist": "Maroon 5 | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 4, "URL": "https://musicchartsarchive.com/singles/maroon-5/memories"}, {"Single Title": "\"10,000 Hours\" Song", "Artist": "<PERSON> <PERSON> <PERSON> & <PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 5, "URL": "https://musicchartsarchive.com/singles/dan-and-shay/10000-hours"}, {"Single Title": "\"Someone You Loved\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 6, "URL": "https://musicchartsarchive.com/singles/lewis-capaldi/someone-you-loved"}, {"Single Title": "\"Dance Monkey\" Song", "Artist": "Tones And I | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 7, "URL": "https://musicchartsarchive.com/singles/tones-and-i/dance-monkey"}, {"Single Title": "\"Good As Hell\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 8, "URL": "https://musicchartsarchive.com/singles/lizzo/good-as-hell"}, {"Single Title": "\"<PERSON><PERSON><PERSON>\" Song", "Artist": "Arizona Zervas | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 9, "URL": "https://musicchartsarchive.com/singles/arizona-zervas/roxanne"}, {"Single Title": "\"Lose You To Love Me\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 10, "URL": "https://musicchartsarchive.com/singles/selena-gomez/lose-you-to-love-me"}, {"Single Title": "\"Ballin'\" Song", "Artist": "<PERSON>ard feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 11, "URL": "https://musicchartsarchive.com/singles/dj-mustard/ballin"}, {"Single Title": "\"Bop\" Song", "Artist": "DaBaby | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 12, "URL": "https://musicchartsarchive.com/singles/dababy/bop"}, {"Single Title": "\"Highest In The Room\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 13, "URL": "https://musicchartsarchive.com/singles/travis-scott/highest-in-the-room"}, {"Single Title": "\"Don't Start Now\" Song", "Artist": "Dua Lipa | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 14, "URL": "https://musicchartsarchive.com/singles/dua-lipa/dont-start-now"}, {"Single Title": "\"No Guidance\" Song", "Artist": "<PERSON> & Drake | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 15, "URL": "https://musicchartsarchive.com/singles/chris-brown/no-guidance"}, {"Single Title": "\"Trampoline\" Song", "Artist": "Shaed | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 16, "URL": "https://musicchartsarchive.com/singles/shaed/trampoline"}, {"Single Title": "\"Heartless\" Song", "Artist": "The Weeknd | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 17, "URL": "https://musicchartsarchive.com/singles/the-weeknd/heartless"}, {"Single Title": "\"Senorita\" Song", "Artist": "<PERSON> & Camila C<PERSON>llo | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 18, "URL": "https://musicchartsarchive.com/singles/shawn-mendes/senorita"}, {"Single Title": "\"Everything I Wanted\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 19, "URL": "https://musicchartsarchive.com/singles/billie-eilish/everything-i-wanted"}, {"Single Title": "\"Truth Hurts\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 20, "URL": "https://musicchartsarchive.com/singles/lizzo/truth-hurts"}, {"Single Title": "\"Hot\" Song", "Artist": "Young Thug feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 21, "URL": "https://musicchartsarchive.com/singles/young-thug/hot"}, {"Single Title": "\"Woah\" Song", "Artist": "Lil Baby | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 22, "URL": "https://musicchartsarchive.com/singles/lil-baby/woah"}, {"Single Title": "\"Panini\" Song", "Artist": "Lil Nas X | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 23, "URL": "https://musicchartsarchive.com/singles/lil-nas-x/panini"}, {"Single Title": "\"Adore You\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 24, "URL": "https://musicchartsarchive.com/singles/harry-styles/adore-you"}, {"Single Title": "\"Only Human\" Song", "Artist": "Jonas Brothers | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 25, "URL": "https://musicchartsarchive.com/singles/jonas-brothers/only-human"}, {"Single Title": "\"The Bones\" Song", "Artist": "<PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 26, "URL": "https://musicchartsarchive.com/singles/maren-morris/the-bones"}, {"Single Title": "\"One Man Band\" Song", "Artist": "Old Dominion | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 27, "URL": "https://musicchartsarchive.com/singles/old-dominion/one-man-band"}, {"Single Title": "\"Bandit\" Song", "Artist": "Juice WRLD & YoungBoy Never Broke Again | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 28, "URL": "https://musicchartsarchive.com/singles/juice-wrld/bandit"}, {"Single Title": "\"Hot Girl Bummer\" Song", "Artist": "blackbear | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 29, "URL": "https://musicchartsarchive.com/singles/blackbear/hot-girl-bummer"}, {"Single Title": "\"Futsal Shuffle 2020\" Song", "Artist": "<PERSON> U<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 30, "URL": "https://musicchartsarchive.com/singles/lil-uzi-vert/futsal-shuffle-2020"}, {"Single Title": "\"Even Though I'm Leaving\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 31, "URL": "https://musicchartsarchive.com/singles/luke-combs/even-though-im-leaving"}, {"Single Title": "\"I Don't Care\" Song", "Artist": "<PERSON> & <PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 32, "URL": "https://musicchartsarchive.com/singles/ed-sheeran/i-dont-care"}, {"Single Title": "\"Bad Guy\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 33, "URL": "https://musicchartsarchive.com/singles/billie-eilish/bad-guy"}, {"Single Title": "\"Sucker\" Song", "Artist": "Jonas Brothers | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 34, "URL": "https://musicchartsarchive.com/singles/jonas-brothers/sucker"}, {"Single Title": "\"High Fashion\" Song", "Artist": "<PERSON><PERSON> feat. <PERSON><PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 35, "URL": "https://musicchartsarchive.com/singles/roddy-ricch/high-fashion"}, {"Single Title": "\"Lover\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 36, "URL": "https://musicchartsarchive.com/singles/taylor-swift/lover"}, {"Single Title": "\"On Chill\" Song", "Artist": "Wale feat Jeremih | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 37, "URL": "https://musicchartsarchive.com/singles/wale/on-chill"}, {"Single Title": "\"Falling\" Song", "Artist": "<PERSON> | Music Charts Archive", "Chart Date": "2020-01-18", "Position": 38, "URL": "https://musicchartsarchive.com/singles/trevor-daniel/falling"}]