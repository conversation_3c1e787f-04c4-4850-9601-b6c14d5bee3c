# 🛠️ Supabase Table তৈরি করার গাইড

আপনার Supabase database এ manually table তৈরি করার জন্য এই guide follow করুন।

## 📋 Step-by-Step Instructions

### 1. Supabase Dashboard খুলুন
- যান: https://supabase.com/dashboard
- আপনার project select করুন

### 2. Singles Charts Table তৈরি করুন

1. **Table Editor** এ যান (left sidebar)
2. **"Create a new table"** click করুন
3. **Table name:** `singles_charts`
4. **এই columns গুলো add করুন:**

| Column Name | Type | Settings |
|-------------|------|----------|
| `id` | `int8` | ✅ Primary Key, ✅ Auto-increment |
| `single_title` | `text` | ✅ Required |
| `artist` | `text` | ✅ Required |
| `chart_date` | `date` | ✅ Required |
| `position` | `int4` | ✅ Required |
| `url` | `text` | Optional |
| `created_at` | `timestamptz` | Default: `now()` |
| `updated_at` | `timestamptz` | Default: `now()` |

5. **"Save"** click করুন

### 3. Albums Charts Table তৈরি করুন

1. আবার **"Create a new table"** click করুন
2. **Table name:** `albums_charts`
3. **এই columns গুলো add করুন:**

| Column Name | Type | Settings |
|-------------|------|----------|
| `id` | `int8` | ✅ Primary Key, ✅ Auto-increment |
| `album_title` | `text` | ✅ Required |
| `artist` | `text` | ✅ Required |
| `chart_date` | `date` | ✅ Required |
| `position` | `int4` | ✅ Required |
| `url` | `text` | Optional |
| `created_at` | `timestamptz` | Default: `now()` |
| `updated_at` | `timestamptz` | Default: `now()` |

4. **"Save"** click করুন

## 🔧 Alternative: SQL Method

যদি আপনি SQL prefer করেন, তাহলে **SQL Editor** এ যান এবং এটা run করুন:

```sql
-- Copy করুন supabase_tables.sql file এর content এবং paste করুন
```

## ✅ Setup Test করুন

Table তৈরি করার পর এটা run করুন:

```bash
cd Project_music_charts
python create_supabase_tables.py
```

আপনি এটা দেখতে পাবেন:
```
✅ singles_charts table exists!
✅ albums_charts table exists!
🎉 All tests passed! Your Supabase setup is working correctly.
```

## 🚀 আপনার Scrapers Run করুন

Table তৈরি হয়ে গেলে, আপনার scrapers কাজ করবে:

```bash
cd Project_music_charts
python singles_scraper.py
```

এখন improved code এ এই features আছে:
- ✅ Table exist করে কিনা check করে
- ✅ Data small batches এ insert করে (10 records at a time)
- ✅ Data validate করে insert করার আগে
- ✅ কিছু batch fail হলেও scraping continue করে
- ✅ Detailed error messages দেয়

## 🔍 আপনার Data Monitor করুন

Scraping এর পর, Supabase এ আপনার data check করুন:
1. **Table Editor** এ যান
2. `singles_charts` বা `albums_charts` click করুন
3. আপনার scraped data দেখুন!

আপনি **SQL Editor** এ queries ও run করতে পারেন:
```sql
-- Total records count
SELECT COUNT(*) FROM singles_charts;

-- Latest chart data
SELECT * FROM singles_charts 
ORDER BY chart_date DESC 
LIMIT 10;

-- Top artists by chart entries
SELECT artist, COUNT(*) as chart_entries 
FROM singles_charts 
GROUP BY artist 
ORDER BY chart_entries DESC 
LIMIT 10;
```

## 🎯 Next Steps

1. **Tables তৈরি করুন** (উপরের guide follow করে)
2. **Test script run করুন** (`python create_supabase_tables.py`)
3. **Scrapers run করুন** (`python singles_scraper.py`)
4. **Supabase dashboard এ data check করুন**

এখন আপনার music charts data professional database এ store হবে! 🎉
