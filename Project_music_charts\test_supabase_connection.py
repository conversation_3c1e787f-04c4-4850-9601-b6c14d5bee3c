import os
import sys
from datetime import datetime, date
from dotenv import load_dotenv
from supabase import create_client, Client

load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

print(f"SUPABASE_URL: {SUPABASE_URL}")
print(f"SUPABASE_KEY: {'Set' if SUPABASE_KEY else 'Not Set'}")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("ERROR: Please set SUPABASE_URL and SUPABASE_KEY environment variables.")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def test_insert_sample_data():
    """Test inserting sample data into singles_charts table"""
    
    sample_data = [
        {
            "single_title": "Test Song 1",
            "artist": "Test Artist 1",
            "chart_date": "2025-01-01",
            "position": 1,
            "url": "https://example.com/test1"
        },
        {
            "single_title": "Test Song 2", 
            "artist": "Test Artist 2",
            "chart_date": "2025-01-01",
            "position": 2,
            "url": "https://example.com/test2"
        }
    ]
    
    try:
        print("Testing data insertion...")
        result = supabase.table("singles_charts").insert(sample_data).execute()
        
        if result.data:
            print(f"Successfully inserted {len(result.data)} test records!")
            print("Sample inserted data:")
            for record in result.data:
                print(f"  - {record['single_title']} by {record['artist']}")
            return True
        else:
            print("No data was inserted")
            return False
            
    except Exception as e:
        print(f"Error inserting test data: {e}")
        return False

def test_read_data():
    """Test reading data from singles_charts table"""
    
    try:
        print("\nTesting data reading...")
        result = supabase.table("singles_charts").select("*").limit(5).execute()
        
        if result.data:
            print(f"Successfully read {len(result.data)} records!")
            print("Sample records:")
            for record in result.data:
                print(f"  - {record['single_title']} by {record['artist']} (Position: {record['position']})")
            return True
        else:
            print("No records found in the table")
            return True
            
    except Exception as e:
        print(f"Error reading data: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    
    try:
        print("\nCleaning up test data...")
        result = supabase.table("singles_charts").delete().like("single_title", "Test Song%").execute()
        print("Test data cleaned up!")
        return True
    except Exception as e:
        print(f"Error cleaning up test data: {e}")
        return False

def main():
    print("Testing Supabase Connection...")
    print(f"Connecting to: {SUPABASE_URL[:50]}...")
    
    insert_success = test_insert_sample_data()
    
    if insert_success:
        read_success = test_read_data()

        cleanup_test_data()
        
        if read_success:
            print("\nAll tests passed! Your Supabase connection is working correctly.")
            print("You can now run your music charts scraper!")
            print("\nNext steps:")
            print("1. python singles_scraper.py")
            print("2. python albums_scraper.py")
        else:
            print("\nInsert worked but read failed. Check your table permissions.")
    else:
        print("\nConnection test failed. Please check:")
        print("1. Your SUPABASE_URL and SUPABASE_KEY in .env file")
        print("2. Run the SQL script (supabase_tables.sql) in your Supabase SQL Editor")
        print("3. Make sure your Supabase project is active")
        print("4. Check MANUAL_TABLE_CREATION.md for detailed instructions")

if __name__ == "__main__":
    main()
