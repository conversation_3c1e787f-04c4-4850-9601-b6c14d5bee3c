FROM python:3.12-slim

WORKDIR /metademy_ai/scaper/Musiccharts_scraper

# Install dependencies first (better caching)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the script
COPY main.py /metademy_ai/scaper/Musiccharts_scraper/main.py

# Create data directories
RUN mkdir -p /metademy_ai/scaper/Musiccharts_scraper/progress_tracking
RUN mkdir -p /metademy_ai/scaper/Musiccharts_scraper/music_charts_data

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Run the scraper
CMD ["python", "/metademy_ai/scaper/Musiccharts_scraper/main.py"]
