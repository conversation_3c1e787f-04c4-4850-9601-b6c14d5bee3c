# Docker Compose configuration for Music Charts Scraper
services:
  music-charts-scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: music-charts-scraper
    volumes:
      - ./music_charts_data:/metademy_ai/scaper/Musiccharts_scraper/music_charts_data
      - ./progress_tracking:/metademy_ai/scaper/Musiccharts_scraper/progress_tracking
      - ./.env:/metademy_ai/scaper/Musiccharts_scraper/.env
      - ./main.py:/metademy_ai/scaper/Musiccharts_scraper/main.py
    restart: unless-stopped
    environment:
      # You can override these in the .env file
      - DECADES=1970s,1980s,1990s,2000s,2010s,2020s
      - CHART_TYPES=singles,albums
    # Healthcheck to monitor the container
    healthcheck:
      test: ["CMD", "ls", "/metademy_ai/scaper/Musiccharts_scraper/music_charts_data"]
      interval: 1m
      timeout: 10s
      retries: 3
      start_period: 30s
