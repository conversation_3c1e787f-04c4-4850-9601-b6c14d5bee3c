# 🎵 Music Charts Scraper - Supabase Integration Guide

Complete guide to connect your music charts scraper with Supabase database.

## 📋 Prerequisites

✅ You should have:
- Supabase account and project
- SUPABASE_URL and SUPABASE_KEY in your `.env` file
- Python packages installed (supabase, python-dotenv, etc.)

## 🚀 Setup Steps

### Step 1: Create Database Tables

**Option 1: Use SQL Editor**
1. Go to Supabase Dashboard: https://supabase.com/dashboard
2. Select your project
3. Go to "SQL Editor" (left sidebar)
4. Copy the content from `supabase_tables.sql` file
5. Paste in SQL Editor and click "Run"

**Option 2: Manual Table Creation**
- Follow the instructions in `MANUAL_TABLE_CREATION.md` file

### Step 2: Test Connection

After creating tables, run this command:

```bash
cd Project_music_charts
python test_supabase_connection.py
```

Expected output:
```
🧪 Testing data insertion...
✅ Successfully inserted 2 test records!
📖 Testing data reading...
✅ Successfully read 2 records!
🧹 Cleaning up test data...
✅ Test data cleaned up!
🎉 All tests passed! Your Supabase connection is working correctly.
```

### Step 3: Run Your Scrapers

Now run your scrapers and data will be stored in Supabase:

**For Singles Charts:**
```bash
cd Project_music_charts
python singles_scraper.py
```

**For Albums Charts:**
```bash
cd Project_music_charts
python albums_scraper.py
```

## 📊 Database Schema

### Singles Charts Table (`singles_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| single_title | TEXT | Song title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

### Albums Charts Table (`albums_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| album_title | TEXT | Album title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

## 🔍 Data Query Examples

### Basic Queries

**Top 10 singles for a specific date:**
```sql
SELECT single_title, artist, position
FROM singles_charts
WHERE chart_date = '2025-01-01'
ORDER BY position
LIMIT 10;
```

**All #1 hits by an artist:**
```sql
SELECT single_title, chart_date
FROM singles_charts
WHERE artist = 'Taylor Swift' AND position = 1
ORDER BY chart_date DESC;
```

**Chart statistics:**
```sql
SELECT * FROM get_chart_stats();
```

## 🛠️ Features

### ✅ What's Working
- **Automatic data insertion** to Supabase during scraping
- **Local backup** - Data is still saved to JSON files
- **Duplicate prevention** - Unique constraints prevent duplicate entries
- **Error handling** - Continues scraping even if Supabase fails
- **Progress tracking** - Resume scraping from where you left off

### 🔄 Data Flow
1. Scraper extracts data from website
2. Data is pushed to Supabase database
3. Data is also saved locally as JSON backup
4. Progress is tracked and can be resumed

## 🚨 Troubleshooting

### Common Issues

**1. Connection Failed**
```
❌ Supabase connection test failed
```
- Check your SUPABASE_URL and SUPABASE_KEY in `.env` file
- Verify your Supabase project is active
- Make sure you ran the SQL script to create tables

**2. Insert Error**
```
❌ Supabase insert error: relation "singles_charts" does not exist
```
- Run the `supabase_tables.sql` script in your Supabase SQL Editor

**3. Permission Error**
```
❌ Error: insufficient_privilege
```
- Check your Supabase API key has the correct permissions
- Make sure you're using the service role key for server-side operations

## 📈 Monitoring

### View Your Data
- **Supabase Dashboard**: Table Editor → View your tables
- **Local Files**: Check JSON files in `singles_charts_data/` and `albums_charts_data/`

### Statistics
Run this query to see your data stats:
```sql
SELECT 
  'singles' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM singles_charts
UNION ALL
SELECT 
  'albums' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM albums_charts;
```

## 🎯 Next Steps

1. **Run the test script** to verify setup
2. **Start scraping** with updated scrapers
3. **Monitor progress** in Supabase dashboard
4. **Query your data** for analysis

Your music charts data will now be stored in a professional database with full SQL capabilities! 🎉

## 📁 Files Overview

- `singles_scraper.py` - Updated with Supabase integration
- `albums_scraper.py` - Updated with Supabase integration
- `supabase_tables.sql` - SQL script to create tables
- `create_supabase_tables.py` - Python script to check tables
- `test_supabase_connection.py` - Test your setup
- `MANUAL_TABLE_CREATION.md` - Manual table creation guide
- `SUPABASE_SETUP_GUIDE.md` - This complete guide
