# 🎵 Music Charts Scraper - Supabase Integration Guide

আপনার music charts scraper কে Supabase database এর সাথে connect করার complete guide।

## 📋 Prerequisites

✅ আপনার কাছে আছে:
- Supabase account এবং project
- SUPABASE_URL এবং SUPABASE_KEY আপনার `.env` file এ
- Python packages installed (supabase, python-dotenv, etc.)

## 🚀 Setup Steps

### Step 1: Database Tables তৈরি করুন

**Option 1: SQL Editor ব্যবহার করুন**
1. Supabase Dashboard এ যান: https://supabase.com/dashboard
2. আপনার project select করুন
3. "SQL Editor" এ যান (left sidebar)
4. `supabase_tables.sql` file এর content copy করুন
5. SQL Editor এ paste করে "Run" click করুন

**Option 2: Manual Table Creation**
- `MANUAL_TABLE_CREATION.md` file এর instructions follow করুন

### Step 2: Connection Test করুন

Tables তৈরি করার পর এই command run করুন:

```bash
cd Project_music_charts
python test_supabase_connection.py
```

Expected output:
```
🧪 Testing data insertion...
✅ Successfully inserted 2 test records!
📖 Testing data reading...
✅ Successfully read 2 records!
🧹 Cleaning up test data...
✅ Test data cleaned up!
🎉 All tests passed! Your Supabase connection is working correctly.
```

### Step 3: Scrapers Run করুন

এখন আপনার scrapers run করুন এবং data Supabase এ store হবে:

**Singles Charts এর জন্য:**
```bash
cd Project_music_charts
python singles_scraper.py
```

**Albums Charts এর জন্য:**
```bash
cd Project_music_charts
python albums_scraper.py
```

## 📊 Database Schema

### Singles Charts Table (`singles_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| single_title | TEXT | Song title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

### Albums Charts Table (`albums_charts`)
| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key |
| album_title | TEXT | Album title |
| artist | TEXT | Artist name |
| chart_date | DATE | Chart date (YYYY-MM-DD) |
| position | INTEGER | Chart position |
| url | TEXT | Source URL |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |

## 🔍 Data Query করার Examples

### Basic Queries

**Specific date এর top 10 singles:**
```sql
SELECT single_title, artist, position 
FROM singles_charts 
WHERE chart_date = '2025-01-01' 
ORDER BY position 
LIMIT 10;
```

**কোন artist এর সব #1 hits:**
```sql
SELECT single_title, chart_date 
FROM singles_charts 
WHERE artist = 'Taylor Swift' AND position = 1 
ORDER BY chart_date DESC;
```

**Chart statistics:**
```sql
SELECT * FROM get_chart_stats();
```

## 🛠️ Features

### ✅ যা কাজ করছে
- **Automatic data insertion** Supabase এ scraping এর সময়
- **Local backup** - Data এখনও JSON files এ save হয়
- **Duplicate prevention** - Unique constraints duplicate entries prevent করে
- **Error handling** - Supabase fail হলেও scraping continue করে
- **Progress tracking** - যেখানে ছেড়েছিলেন সেখান থেকে resume করতে পারবেন

### 🔄 Data Flow
1. Scraper website থেকে data extract করে
2. Data Supabase database এ push করে
3. Data local JSON backup হিসেবেও save করে
4. Progress track করে এবং resume করা যায়

## 🚨 Troubleshooting

### Common Issues

**1. Connection Failed**
```
❌ Supabase connection test failed
```
- আপনার `.env` file এ SUPABASE_URL এবং SUPABASE_KEY check করুন
- Supabase project active আছে কিনা verify করুন
- SQL script run করে tables create করেছেন কিনা check করুন

**2. Insert Error**
```
❌ Supabase insert error: relation "singles_charts" does not exist
```
- Supabase SQL Editor এ `supabase_tables.sql` script run করুন

**3. Permission Error**
```
❌ Error: insufficient_privilege
```
- Supabase API key এর correct permissions আছে কিনা check করুন
- Service role key ব্যবহার করছেন কিনা নিশ্চিত হন

## 📈 Monitoring

### আপনার Data দেখুন
- **Supabase Dashboard**: Table Editor → আপনার tables view করুন
- **Local Files**: `singles_charts_data/` এবং `albums_charts_data/` এ JSON files check করুন

### Statistics
এই query run করে আপনার data stats দেখুন:
```sql
SELECT 
  'singles' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM singles_charts
UNION ALL
SELECT 
  'albums' as type,
  COUNT(*) as total_records,
  COUNT(DISTINCT artist) as unique_artists,
  MIN(chart_date) as earliest_date,
  MAX(chart_date) as latest_date
FROM albums_charts;
```

## 🎯 Next Steps

1. **Test script run করুন** setup verify করার জন্য
2. **Scraping শুরু করুন** updated scrapers দিয়ে
3. **Progress monitor করুন** Supabase dashboard এ
4. **Data query করুন** analysis এর জন্য

এখন আপনার music charts data professional database এ full SQL capabilities সহ store হবে! 🎉

## 📁 Files Overview

- `singles_scraper.py` - Updated with Supabase integration
- `albums_scraper.py` - Updated with Supabase integration
- `supabase_tables.sql` - SQL script to create tables
- `create_supabase_tables.py` - Python script to check tables
- `test_supabase_connection.py` - Test your setup
- `MANUAL_TABLE_CREATION.md` - Manual table creation guide
- `SUPABASE_SETUP_GUIDE.md` - This complete guide
